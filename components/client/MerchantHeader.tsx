'use client';

import Link from 'next/link';
import { Store, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import AddPaymentMethodDialog from '../customized/alert-dialog/alert-dialog-05';

type MerchantHeaderProps = {
  merchantName: string;
  merchantId: string;
};

export default function MerchantHeader({ merchantName, merchantId }: MerchantHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
      <div className="flex items-center gap-3">
        <Store className="h-8 w-8 text-primary" />
        <div>
          <h1 className="text-2xl font-bold">{merchantName}</h1>
          <p className="text-sm text-muted-foreground">Merchant ID: {merchantId}</p>
        </div>
      </div>
      
      <div className='flex items-center gap-2 flex-col md:flex-row'>
      <AddPaymentMethodDialog />
      <Button variant="outline" size="sm" asChild>
        <Link href="/dashboard" className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Link>
      </Button>
      </div>
    </div>
  );
}
