'use client';

import { CreditCard, ChevronDown, Clock, DollarSign, User, Hash } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

type Transaction = {
  id: string;
  endpoint: string;
  merchantId: string;
  merchantName: string;
  amount: any; // Decimal in Prisma
  mobileNumber: string;
  clientReference: string;
  paymentType: string;
  timestamp: Date;
};

type TransactionListProps = {
  transactions: Transaction[];
};

export default function TransactionList({ transactions }: TransactionListProps) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LSL',
      minimumFractionDigits: 2,
    }).format(typeof amount === 'string' ? parseFloat(amount) : amount);
  };

  const formatPaymentType = (paymentType: string): string => {
    switch (paymentType.toUpperCase()) {
      case 'MPESA':
        return 'M-Pesa';
      case 'ECOCASH':
        return 'EcoCash';
      default:
        return paymentType;
    }
  };

  const getPaymentTypeBadgeClass = (paymentType: string): string => {
    const normalizedType = paymentType.toUpperCase();
    switch (normalizedType) {
      case 'MPESA':
      case 'M-PESA':
        return 'bg-red-500 text-white hover:bg-red-600';
      case 'ECOCASH':
        return 'bg-blue-500 text-white hover:bg-blue-950';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  };

  return (
    <div>
      {transactions.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No failed transactions found for this merchant.</p>
        </div>
      ) : (
        <Accordion type="single" collapsible className="w-full">
          {transactions.map((transaction, index) => (
            <AccordionItem key={transaction.id} value={transaction.id}>
              <AccordionTrigger className="hover:bg-muted/50 px-4 py-3 rounded-md">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between w-full text-left gap-2">
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full flex items-center gap-1 w-fit ${getPaymentTypeBadgeClass(transaction.paymentType)}`}>
                      <CreditCard className="h-3 w-3" />
                      {formatPaymentType(transaction.paymentType)}
                    </span>
                    <span className="text-sm font-medium">{transaction.clientReference}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-bold">{formatCurrency(transaction.amount)}</span>
                    <span className="text-xs text-muted-foreground">{formatDate(transaction.timestamp)}</span>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="bg-muted/30 p-4 rounded-md space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <DetailItem
                      icon={<User className="h-4 w-4 text-muted-foreground" />}
                      label="Customer Phone"
                      value={transaction.mobileNumber}
                    />
                    <DetailItem
                      icon={<Hash className="h-4 w-4 text-muted-foreground" />}
                      label="Transaction ID"
                      value={transaction.id}
                    />
                    <DetailItem
                      icon={<p className="h-4 w-4 text-muted-foreground">M</p>}
                      label="Amount"
                      value={formatCurrency(transaction.amount)}
                    />
                    <DetailItem
                      icon={<Clock className="h-4 w-4 text-muted-foreground" />}
                      label="Timestamp"
                      value={formatDate(transaction.timestamp)}
                    />
                  </div>

                  <div className="pt-2 border-t border-border">
                    <h4 className="text-sm font-medium mb-2">Technical Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <DetailItem
                        icon={<Hash className="h-4 w-4 text-muted-foreground" />}
                        label="Client Reference"
                        value={transaction.clientReference}
                      />
                      <DetailItem
                        icon={<Hash className="h-4 w-4 text-muted-foreground" />}
                        label="Endpoint"
                        value={transaction.endpoint}
                      />
                    </div>
                  </div>

                  <div className="pt-2 border-t border-border">
                    <h4 className="text-sm font-medium mb-2">Failure Reason</h4>
                    <p className="text-sm text-muted-foreground">
                      This transaction failed due to a timeout. The payment was processed by the payment provider,
                      but your system did not receive the confirmation in time. The customer was charged, but the
                      transaction was not marked as successful in your system.
                    </p>
                  </div>

                  <div className="pt-2 border-t border-border">
                    <h4 className="text-sm font-medium mb-2">Recommended Action</h4>
                    <p className="text-sm text-muted-foreground">
                      Verify this payment with the payment provider and manually mark it as successful in your system.
                      Contact the customer to inform them that their payment was received despite any error message they may have seen.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}

type DetailItemProps = {
  icon: React.ReactNode;
  label: string;
  value: string;
};

function DetailItem({ icon, label, value }: DetailItemProps) {
  return (
    <div className="flex items-start gap-2">
      <div className="mt-0.5">{icon}</div>
      <div>
        <p className="text-xs text-muted-foreground">{label}</p>
        <p className="text-sm font-medium break-all">{value}</p>
      </div>
    </div>
  );
}
