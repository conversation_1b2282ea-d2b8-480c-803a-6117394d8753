"use client";

import { CreditCard, AlertTriangle, Clock, DollarSign } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { upsertPaymentMethodAvailability } from "@/actions/paymentMethodAvailabilityActions";
import { useState } from "react";
import { toast } from "sonner";
import { Switch } from "../ui/switch";
import SpinnerCircle3 from "../customized/spinner/spinner-09";

type MerchantStatsProps = {
  stats: {
    merchantName: string;
    totalCount: number;
    paymentTypeStats: any[];
    totalAmount: number;
    recentActivity: number;
  };

  mpesaAvailability: {
    id: string;
    paymentMethod: string;
    isAvailable: boolean;
  };
  ecocashAvailability: {
    id: string;
    paymentMethod: string;
    isAvailable: boolean;
  };
};

export default function MerchantStats({
  stats,
  mpesaAvailability,
  ecocashAvailability,
}: MerchantStatsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [mpesaIsAvailable, setMpesaIsAvailable] = useState(
    mpesaAvailability.isAvailable
  );
  const [ecocashIsAvailable, setEcocashIsAvailable] = useState(
    ecocashAvailability.isAvailable
  );

  const handleEcoCashAvailabilityToggle = async (isAvailable: boolean) => {
    setIsLoading(true);
    const response = await upsertPaymentMethodAvailability(
      "ECOCASH",
      isAvailable,
      ecocashAvailability.id
    );
    if (typeof response == "string") {
      console.error("Error toggling payment method availability:", response);
      toast.error(response);
    }
    setEcocashIsAvailable(isAvailable);
    setIsLoading(false);
  };

  const handleMpesaAvailabilityToggle = async (isAvailable: boolean) => {
    setIsLoading(true);
    const response = await upsertPaymentMethodAvailability(
      "MPESA",
      isAvailable,
      mpesaAvailability.id
    );

    if (typeof response == "string") {
      console.error("Error toggling payment method availability:", response);
      toast.error(response);
    }
    setMpesaIsAvailable(isAvailable);
    setIsLoading(false);
  };

  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "LSL",
      minimumFractionDigits: 2,
    }).format(typeof amount === "string" ? parseFloat(amount) : amount);
  };

  // Format payment type for display
  const formatPaymentType = (paymentType: string): string => {
    switch (paymentType.toUpperCase()) {
      case "MPESA":
        return "M-Pesa";
      case "ECOCASH":
        return "EcoCash";
      default:
        return paymentType;
    }
  };

  // Find M-Pesa and EcoCash counts
  const mPesaCount =
    stats.paymentTypeStats.find(
      (stat: any) =>
        stat.paymentType.toUpperCase() === "MPESA" ||
        stat.paymentType === "M-Pesa"
    )?.count || 0;

  const ecoCashCount =
    stats.paymentTypeStats.find(
      (stat: any) =>
        stat.paymentType.toUpperCase() === "ECOCASH" ||
        stat.paymentType === "EcoCash"
    )?.count || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Total Failed Payments"
        value={stats.totalCount.toString()}
        icon={<AlertTriangle className="h-5 w-5 text-amber-500" />}
        description="All time failed transactions"
        className="border-amber-200 dark:border-amber-800"
      />

      <StatCard
        title="Total Amount"
        value={formatCurrency(Number(stats.totalAmount))}
        icon={<p className="h-5 w-5 text-green-500 text-center">M</p>}
        description="Value of failed payments"
        className="border-red-200 dark:border-red-800"
      />

      <StatCard
        title="M-Pesa Failures"
        value={mPesaCount.toString()}
        icon={<CreditCard className="h-5 w-5 text-green-500" />}
        description="M-Pesa failed transactions"
        className="border-green-200 dark:border-green-800"
        paymentMethod="MPESA"
        isAvailable={mpesaIsAvailable}
        isLoading={isLoading}
        onToggle={handleMpesaAvailabilityToggle}
      />

      <StatCard
        title="EcoCash Failures"
        value={ecoCashCount.toString()}
        icon={<CreditCard className="h-5 w-5 text-purple-500" />}
        description="EcoCash failed transactions"
        className="border-purple-200 dark:border-purple-800"
        paymentMethod="ECOCASH"
        isAvailable={ecocashIsAvailable}
        isLoading={isLoading}
        onToggle={handleEcoCashAvailabilityToggle}
      />
    </div>
  );
}

type StatCardProps = {
  title: string;
  value: string;
  icon: React.ReactNode;
  description: string;
  className?: string;
  paymentMethod?: string;
  isAvailable?: boolean;
  isLoading?: boolean;
  onToggle?: (isAvailable: boolean) => void;
};

function StatCard({
  title,
  value,
  icon,
  description,
  className,
  paymentMethod,
  isAvailable,
  isLoading,
  onToggle,
}: StatCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <div className="rounded-full bg-background p-2 shadow-sm">{icon}</div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-2xl font-bold">{value}</p>
        <CardDescription className="text-xs mt-1 flex justify-between">
          {description}
          {isLoading ? (
            <SpinnerCircle3 />
          ) : (
            paymentMethod && (
              <div className="flex gap-2">
                {isAvailable ? "Available" : "Unavailable"}
                <Switch
                  checked={isAvailable}
                  onCheckedChange={onToggle}
                  disabled={isLoading}
                />
              </div>
            )
          )}
        </CardDescription>
      </CardContent>
    </Card>
  );
}
