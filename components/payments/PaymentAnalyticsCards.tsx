'use client';

import { CheckCircle, DollarSign, TrendingUp, CreditCard, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PaymentAnalytics } from '@/actions/getSuccessfulPaymentsActions';

type PaymentAnalyticsCardsProps = {
  analytics: PaymentAnalytics;
};

export default function PaymentAnalyticsCards({ analytics }: PaymentAnalyticsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LSL',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Successful Payments */}
      <Card className="bg-blue-50 dark:bg-blue-950">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Successful Orders
            </CardTitle>
            <div className="rounded-full bg-background p-2 shadow-sm">
              <CheckCircle className="h-5 w-5 text-blue-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">{analytics.totalSuccessfulPayments}</p>
          <CardDescription className="text-xs mt-1">
            All successful orders
          </CardDescription>
        </CardContent>
      </Card>

      {/* Total Amount */}
      <Card className="bg-green-50 dark:bg-green-950">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Amount
            </CardTitle>
            <div className="rounded-full bg-background p-2 shadow-sm">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">{formatCurrency(analytics.totalAmount)}</p>
          <CardDescription className="text-xs mt-1">
            Total payment value processed
          </CardDescription>
        </CardContent>
      </Card>

      {/* Total Service Fees */}
      <Card className="bg-emerald-50 dark:bg-emerald-950">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Service Fees
            </CardTitle>
            <div className="rounded-full bg-background p-2 shadow-sm">
              <TrendingUp className="h-5 w-5 text-emerald-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-2xl font-bold">{formatCurrency(analytics.serviceFees.totalFees)}</p>
          <CardDescription className="text-xs mt-1">
            8% fees from {analytics.serviceFees.applicablePayments} success
          </CardDescription>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card className="bg-purple-50 dark:bg-purple-950">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Payment Methods
            </CardTitle>
            <div className="rounded-full bg-background p-2 shadow-sm">
              <CreditCard className="h-5 w-5 text-purple-600" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-1">
            {Object.entries(analytics.paymentMethodBreakdown).map(([method, data]) => (
              <div key={method} className="flex justify-between text-sm">
                <span>{method}:</span>
                <span className="font-medium">{data.count}</span>
              </div>
            ))}
          </div>
          <CardDescription className="text-xs mt-1">
            Payment method distribution
          </CardDescription>
        </CardContent>
      </Card>
    </div>
  );
}
