'use client';

import { useState, useEffect } from 'react';
import { filterDevNotifications } from '@/actions/filterDevNotificationsActions';
import { getDevNotifications } from '@/actions/getDevNotificationsActions';
import FilterBar from './FilterBar';
import NotificationsTable from './NotificationsTable';

type DevNotification = {
  id: string;
  endpoint: string;
  merchantId: string;
  merchantName: string;
  amount: any;
  mobileNumber: string;
  clientReference: string;
  paymentType: string;
  timestamp: Date;
};

type DashboardClientProps = {
  initialData: DevNotification[];
  totalItems: number;
};

export default function DashboardClient({
  initialData,
  totalItems,
}: DashboardClientProps) {
  const [data, setData] = useState(initialData);
  const [total, setTotal] = useState(totalItems);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    paymentType: '',
    merchantId: '',
    startDate: undefined,
    endDate: undefined,
    searchTerm: '',
  });

  const itemsPerPage = 10;

  const fetchData = async (page = 1, newFilters = filters) => {
    setLoading(true);
    try {
      const offset = (page - 1) * itemsPerPage;

      let result;

      if (
        !newFilters.paymentType &&
        !newFilters.merchantId &&
        !newFilters.startDate &&
        !newFilters.endDate &&
        !newFilters.searchTerm
      ) {
        // If no filters are applied, use the simpler getDevNotifications
        result = await getDevNotifications(itemsPerPage, offset);
      } else {
        // Otherwise use the filter function
        result = await filterDevNotifications({
          ...newFilters,
          limit: itemsPerPage,
          offset,
        });
      }

      setData(result.notifications);
      setTotal(result.total);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    fetchData(1, newFilters);
  };

  const handlePageChange = (page: number) => {
    fetchData(page);
  };

  return (
    <div>
      <FilterBar onFilterChange={handleFilterChange} />

      {loading ? (
        <div className="h-96 w-full bg-muted animate-pulse rounded-lg mt-6"></div>
      ) : (
        <div className="mt-6">
          <NotificationsTable
            data={data}
            totalItems={total}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
