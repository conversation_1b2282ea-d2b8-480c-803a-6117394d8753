"use client";

import { CreditC<PERSON>, Users, CheckCircle } from "lucide-react";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "../ui/switch";
import { useState } from "react";
import { upsertPaymentMethodAvailability } from "@/actions/paymentMethodAvailabilityActions";
import { toast } from "sonner";
import SpinnerCircle3 from "../customized/spinner/spinner-09";

type StatCardsProps = {
  stats: {
    totalCount: number;
    paymentTypeStats: any[];
    totalAmount: number;
    recentActivity: number;
  };
  mpesaAvailability: {
    id: string;
    paymentMethod: string;
    isActive: boolean;
  } | undefined;
  ecocashAvailability: {
    id: string;
    paymentMethod: string;
    isActive: boolean;
  } | undefined;
};

export default function StatCards({
  stats,
  mpesaAvailability,
  ecocashAvailability,
}: StatCardsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [mpesaisActive, setMpesaisActive] = useState(
    mpesaAvailability &&
    mpesaAvailability.isActive
  );
  const [ecocashisActive, setEcocashisActive] = useState(
    ecocashAvailability &&
    ecocashAvailability.isActive
  );

  const handleEcoCashAvailabilityToggle = async (isActive: boolean) => {
    setIsLoading(true);
    const response = await upsertPaymentMethodAvailability(
      "ECOCASH",
      isActive,
      ecocashAvailability?.id || ""
    );
    if (typeof response == "string") {
      console.error("Error toggling payment method availability:", response);
      toast.error(response);
    }
    setEcocashisActive(isActive);
    setIsLoading(false);
  };

  const handleMpesaAvailabilityToggle = async (isActive: boolean) => {
    setIsLoading(true);
    const response = await upsertPaymentMethodAvailability(
      "MPESA",
      isActive,
      mpesaAvailability?.id || ""
    );

    if (typeof response == "string") {
      console.error("Error toggling payment method availability:", response);
      toast.error(response);
    }
    setMpesaisActive(isActive);
    setIsLoading(false);
  };
  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "LSL",
      minimumFractionDigits: 2,
    }).format(typeof amount === "string" ? parseFloat(amount) : amount);
  };

  // console.log("stats: ", stats)

  // Find M-Pesa and EcoCash counts
  const mPesaCount =
    stats.paymentTypeStats.find((stat: any) => stat.paymentType === "MPESA")
      ?.count || 0;

  const ecoCashCount =
    stats.paymentTypeStats.find((stat: any) => stat.paymentType === "EcoCash")
      ?.count || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="Total Notifications"
        value={stats.totalCount.toString()}
        icon={<Users className="h-5 w-5" />}
        description="All time notifications"
        className="bg-blue-50 dark:bg-blue-950"
      />

      <StatCard
        title="Total Amount"
        value={formatCurrency(Number(stats.totalAmount))}
        icon={<p className="h-5 w-5 text-center text-green-500">M</p>}
        description="Processed payments"
        className="bg-green-50 dark:bg-green-950"
      />

      {mpesaAvailability && (
        <StatCard
          title="M-Pesa Payments"
          value={mPesaCount.toString()}
          icon={<CreditCard className="h-5 w-5" />}
          description="M-Pesa transactions"
          className="bg-purple-50 dark:bg-purple-950"
          paymentMethod="MPESA"
          isActive={mpesaisActive}
          isLoading={isLoading}
          onToggle={handleMpesaAvailabilityToggle}
        />
      )}

      {ecocashAvailability && (
        <StatCard
          title="EcoCash Payments"
          value={ecoCashCount.toString()}
          icon={<CreditCard className="h-5 w-5" />}
          description="EcoCash transactions"
          className="bg-amber-50 dark:bg-amber-950"
          paymentMethod="ECOCASH"
          isActive={ecocashisActive}
          isLoading={isLoading}
          onToggle={handleEcoCashAvailabilityToggle}
        />
      )}

      <Link href="/dashboard/payments/successful" className="block">
        <StatCard
          title="Successful Payments"
          value="View Analytics"
          icon={<CheckCircle className="h-5 w-5 text-green-600" />}
          description="Payment analytics dashboard"
          className="bg-emerald-50 dark:bg-emerald-950 hover:bg-emerald-100 dark:hover:bg-emerald-900 transition-colors cursor-pointer"
        />
      </Link>
    </div>
  );
}

type StatCardProps = {
  title: string;
  value: string;
  icon: React.ReactNode;
  description: string;
  className?: string;
  paymentMethod?: string;
  isActive?: boolean;
  isLoading?: boolean;
  onToggle?: (isActive: boolean) => void;
};

function StatCard({
  title,
  value,
  icon,
  description,
  className,
  paymentMethod,
  isActive,
  isLoading,
  onToggle,
}: StatCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <div className="rounded-full bg-background p-2 shadow-sm">{icon}</div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-2xl font-bold">{value}</p>
        <CardDescription className="text-xs mt-1 flex justify-between">
          {description}
          {isLoading ? (
            <SpinnerCircle3 />
          ) : (
            paymentMethod && (
              <div className="flex gap-2">
                {isActive ? "Available" : "Unavailable"}
                <Switch
                  checked={isActive}
                  onCheckedChange={onToggle}
                  disabled={isLoading}
                />
              </div>
            )
          )}
        </CardDescription>
      </CardContent>
    </Card>
  );
}
