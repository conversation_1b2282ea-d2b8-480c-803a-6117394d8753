'use client';

import Link from 'next/link';
import { ChevronLeft, ChevronRight, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

type DevNotification = {
  id: string;
  endpoint: string;
  merchantId: string;
  merchantName: string;
  amount: any; // Decimal in Prisma
  mobileNumber: string;
  clientReference: string;
  paymentType: string;
  timestamp: Date;
};

type NotificationsTableProps = {
  data: DevNotification[];
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
};

export default function NotificationsTable({
  data,
  totalItems,
  currentPage,
  itemsPerPage,
  onPageChange
}: NotificationsTableProps) {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LSL',
      minimumFractionDigits: 2,
    }).format(typeof amount === 'string' ? parseFloat(amount) : amount);
  };

  const formatPaymentType = (paymentType: string): string => {
    switch (paymentType.toUpperCase()) {
      case 'MPESA':
        return 'M-Pesa';
      case 'ECOCASH':
        return 'EcoCash';
      default:
        return paymentType;
    }
  };

  const getPaymentTypeBadgeClass = (paymentType: string): string => {
    const normalizedType = paymentType.toUpperCase();
    switch (normalizedType) {
      case 'MPESA':
      case 'M-PESA':
        return 'bg-red-500 text-white hover:bg-red-600';
      case 'ECOCASH':
        return 'bg-blue-500 text-white hover:bg-blue-950';
      default:
        return 'bg-gray-500 text-white hover:bg-gray-600';
    }
  };

  return (
    <div className="bg-card rounded-lg shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Merchant</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Mobile</TableHead>
              <TableHead>Payment Type</TableHead>
              <TableHead>Timestamp</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((notification) => (
              <TableRow key={notification.id}>
                <TableCell>
                  <div className="flex flex-col">
                    <Link href={`/client/${notification.merchantId}`} className="font-medium hover:underline">
                      {notification.merchantName}
                    </Link>
                    <div className="text-xs text-muted-foreground">{notification.merchantId}</div>
                  </div>
                </TableCell>
                <TableCell>{notification.clientReference}</TableCell>
                <TableCell className="font-medium">{formatCurrency(notification.amount)}</TableCell>
                <TableCell>{notification.mobileNumber}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full flex items-center gap-1 w-fit ${getPaymentTypeBadgeClass(notification.paymentType)}`}>
                    <CreditCard className="h-3 w-3" />
                    {formatPaymentType(notification.paymentType)}
                  </span>
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {formatDate(notification.timestamp)}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="px-4 py-3 flex items-center justify-between border-t border-border">
        <div className="flex-1 flex justify-between sm:hidden">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-muted-foreground">
              Showing <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span> to{' '}
              <span className="font-medium">
                {Math.min(currentPage * itemsPerPage, totalItems)}
              </span>{' '}
              of <span className="font-medium">{totalItems}</span> results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <Button
                variant="outline"
                size="icon"
                className="rounded-l-md"
                onClick={() => onPageChange(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <span className="sr-only">Previous</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Page numbers would go here */}
              <div className="bg-background border border-input px-4 py-2 text-sm">
                Page {currentPage} of {totalPages}
              </div>

              <Button
                variant="outline"
                size="icon"
                className="rounded-r-md"
                onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                <span className="sr-only">Next</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}
