'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Store, ChevronDown, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { getMerchantList } from '@/actions/filterDevNotificationsActions';

export default function MerchantDropdown() {
  const [merchants, setMerchants] = useState<{ merchantId: string; merchantName: string }[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadMerchants = async () => {
      try {
        setIsLoading(true);
        const merchantList = await getMerchantList();
        setMerchants(merchantList);
      } catch (error) {
        console.error('Error loading merchants:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMerchants();
  }, []);

  const filteredMerchants = merchants.filter(merchant =>
    merchant.merchantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    merchant.merchantId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          <Store className="h-4 w-4" />
          View Merchant
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64">
        <DropdownMenuLabel>Select a Merchant</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link href="/client" className="cursor-pointer font-medium text-primary">
            View All Merchants
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <div className="px-2 py-2">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search merchants..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="max-h-[300px] overflow-y-auto">
          {isLoading ? (
            <div className="px-2 py-4 text-center">
              <p className="text-sm text-muted-foreground">Loading merchants...</p>
            </div>
          ) : filteredMerchants.length === 0 ? (
            <div className="px-2 py-4 text-center">
              <p className="text-sm text-muted-foreground">No merchants found</p>
            </div>
          ) : (
            filteredMerchants.map((merchant) => (
              <DropdownMenuItem key={merchant.merchantId} asChild>
                <Link href={`/client/${merchant.merchantId}`} className="cursor-pointer">
                  <div className="flex flex-col">
                    <span className="font-medium">{merchant.merchantName}</span>
                    <span className="text-xs text-muted-foreground">{merchant.merchantId}</span>
                  </div>
                </Link>
              </DropdownMenuItem>
            ))
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
