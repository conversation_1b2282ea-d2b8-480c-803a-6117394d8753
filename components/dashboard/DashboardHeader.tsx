"use client";

import { LayoutDashboard } from "lucide-react";
import MerchantDropdown from "./MerchantDropdown";
import AddPaymentMethodDialog from "../customized/alert-dialog/alert-dialog-05";

export default function DashboardHeader() {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
      <div className="flex items-center gap-3">
        <LayoutDashboard className="h-8 w-8 text-primary" />
        <h1 className="text-2xl font-bold">
          Mopay Dev Notifications Dashboard
        </h1>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center flex-col md:flex-row gap-2">
          <AddPaymentMethodDialog />
          <MerchantDropdown />
        </div>
        <div className="text-sm text-muted-foreground hidden sm:block">
          Dev Team Portal
        </div>
      </div>
    </div>
  );
}
