"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useState } from "react";
import { toast } from "sonner";
import { upsertPaymentMethodAvailability } from "@/actions/paymentMethodAvailabilityActions";

export default function AddPaymentMethodDialog() {
  const [isLoading, setIsLoading] = useState(false);
  const [isAvailable, setIsAvailable] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState("");

  const handleChange = (value: string) => {
    setPaymentMethod(value);
  };

  const handleSubmit = async() => {
    setIsLoading(true);
    // TODO: Add payment method
    console.log("payment method: ", paymentMethod)
    const response = await upsertPaymentMethodAvailability(
      paymentMethod.toUpperCase(),
      isAvailable,
      ""
    );
    if (typeof response == "string") {
      console.error("Error adding payment method:", response);
      toast.error(response);
      setIsLoading(false)
      return
    }
    setPaymentMethod("");
    setIsLoading(false);
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="outline">
          <Plus className="h-4 w-4" />
          Add Payment Method
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <div className="-mt-3 -mx-6 border-b pb-3 px-6 flex justify-between items-center">
          <AlertDialogTitle>Add Payment Method</AlertDialogTitle>
          <AlertDialogPrimitive.Cancel
            className={buttonVariants({
              variant: "ghost",
              size: "icon",
              className: "!h-7 !w-7",
            })}
          >
            <X />
          </AlertDialogPrimitive.Cancel>
        </div>
        <AlertDialogHeader className="pt-2">
          <AlertDialogTitle>
            <div className="mx-auto sm:mx-0 mb-4 flex h-9 w-9 items-center justify-center rounded-full bg-primary/10">
              <Plus className="h-5 w-5 text-primary" />
            </div>
            Add Payment Method
          </AlertDialogTitle>
          <AlertDialogDescription className="text-[15px]">
            Add a new payment method to your account.
          </AlertDialogDescription>
          <div>
            <Input
              type="text"
              placeholder="Payment Method"
              className="mt-4"
              onChange={(e) => setPaymentMethod(e.target.value)}
            />
            <div className="mt-2 flex justify-end text-xs text-muted-foreground gap-2">
              {isAvailable ? "Available" : "Unavailable"}
              <Switch
                checked={isAvailable}
                onCheckedChange={setIsAvailable}
                disabled={isLoading}
              />
            </div>
          </div>
        </AlertDialogHeader>
        <AlertDialogFooter className="mt-2">
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <Button disabled={isLoading} onClick={handleSubmit}>
            {isLoading ? "Adding..." : "Add Payment Method"}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
