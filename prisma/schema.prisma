generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model DevNotification {
  id              String   @id
  endpoint        String
  merchantId      String
  merchantName    String
  amount          Decimal  @db.Decimal(10, 2)
  mobileNumber    String
  clientReference String
  paymentType     String
  timestamp       DateTime @default(now())
}

model EarlyAccessUsers {
  userId           String   @id @unique
  phoneNumber      String
  socialMediaLinks String[]
  approved         Boolean  @default(false)
  Users            Users    @relation(fields: [userId], references: [userId], onDelete: Cascade)
}

model RegisteredRestaurants {
  restaurantId   String  @id
  name           String
  owner          String
  email          String  @unique
  phone          String
  restaurantType String
  additionalInfo String?
}

model Restaurants {
  restaurantId String @id
  name         String
  email        String @unique
}

model Users {
  userId              String            @id
  firstName           String
  lastName            String
  email               String            @unique
  favouriteRestaurant String
  EarlyAccessUsers    EarlyAccessUsers?
}
