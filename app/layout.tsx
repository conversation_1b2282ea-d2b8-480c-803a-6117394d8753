import type { Metada<PERSON> } from "next";
import { Ubuntu, Ubuntu_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";

const ubuntu = Ubuntu({
  weight: ['300', '400', '500', '700'],
  variable: "--font-ubuntu",
  subsets: ["latin"],
});

const ubuntuMono = Ubuntu_Mono({
  weight: ['400', '700'],
  variable: "--font-ubuntu-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mopay Dev Notifications Dashboard",
  description: "Dashboard for Mopay Development team",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${ubuntu.variable} ${ubuntuMono.variable} font-ubuntu antialiased`}
      >
        {children}
        <Toaster />
      </body>
    </html>
  );
}
