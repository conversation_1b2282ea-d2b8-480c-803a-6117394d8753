import Link from 'next/link';
import { FileQuestion } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

export default function MerchantNotFound() {
  return (
    <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[80vh]">
      <div className="bg-card p-8 rounded-lg shadow-sm max-w-md w-full text-center">
        <FileQuestion className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h2 className="text-2xl font-bold mb-2">Merchant Not Found</h2>
        <p className="text-muted-foreground mb-6">
          The merchant you're looking for doesn't exist or has no failed transactions.
        </p>
        <Button asChild>
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </div>
    </div>
  );
}
