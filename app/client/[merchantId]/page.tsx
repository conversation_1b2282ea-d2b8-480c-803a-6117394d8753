import { Suspense } from "react";
import { notFound } from "next/navigation";
import { AlertTriangle } from "lucide-react";
import {
  getMerchantInfo,
  getMerchantNotifications,
} from "@/actions/getMerchantNotificationsActions";
import MerchantHeader from "@/components/client/MerchantHeader";
import MerchantStats from "@/components/client/MerchantStats";
import TransactionList from "@/components/client/TransactionList";
import { getPaymentMethodAvailability } from "@/actions/paymentMethodAvailabilityActions";
import StatCards from "@/components/dashboard/StatCards";

export default async function MerchantPage({
  params,
}: {
  params: Promise<{ merchantId: string }>;
}) {
  try {
    // Fetch merchant data
    const { merchantId } = await params;
    // const merchantInfo = await getMerchantInfo(merchantId);
    // const notifications = await getMerchantNotifications(merchantId);

    const [
      merchantInfo,
      notifications,
      mpesaAvailability,
      ecocashAvailability,
    ] = await Promise.all([
      getMerchantInfo(merchantId),
      getMerchantNotifications(merchantId),
      getPaymentMethodAvailability("MPESA"),
      getPaymentMethodAvailability("ECOCASH"),
    ]);

    if (!merchantInfo || notifications.length === 0) {
      notFound();
    }

    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <MerchantHeader
            merchantName={merchantInfo.merchantName}
            merchantId={merchantId}
          />
        </div>

        <Suspense
          fallback={
            <div className="h-24 w-full bg-muted animate-pulse rounded-lg"></div>
          }
        >
          <StatCards stats={merchantInfo} mpesaAvailability={mpesaAvailability[0]} ecocashAvailability={ecocashAvailability[0]} />
        </Suspense>

        <div className="mt-8">
          <div className="bg-card p-6 rounded-lg shadow-sm">
            <div className="flex items-center gap-2 mb-4">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              <h2 className="text-lg font-semibold">
                Failed Payment Transactions
              </h2>
            </div>
            <p className="text-muted-foreground mb-6">
              These transactions failed due to timeout issues. The customer was
              charged but the payment confirmation was not received by your
              system in time.
            </p>

            <Suspense
              fallback={
                <div className="h-96 w-full bg-muted animate-pulse rounded-lg"></div>
              }
            >
              <TransactionList transactions={notifications} />
            </Suspense>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error in merchant page:", error);
    throw error;
  }
}
