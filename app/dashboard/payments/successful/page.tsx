import { Suspense } from 'react';
import { <PERSON><PERSON><PERSON>t, CheckCircle, TrendingUp, DollarSign, CreditCard } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getPaymentAnalytics, getSuccessfulPayments } from '@/actions/getSuccessfulPaymentsActions';
import PaymentAnalyticsCards from '@/components/payments/PaymentAnalyticsCards';
import SuccessfulPaymentsTable from '@/components/payments/SuccessfulPaymentsTable';

export default async function SuccessfulPaymentsPage() {
  // Fetch payment analytics and successful payments data
  const [analytics, payments] = await Promise.all([
    getPaymentAnalytics(),
    getSuccessfulPayments(),
  ]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
        <div className="flex items-center gap-3">
          <Link href="/dashboard">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
          <CheckCircle className="h-8 w-8 text-green-600" />
          <h1 className="text-2xl font-bold">
            Successful Payments Analytics
          </h1>
        </div>
        <div className="text-sm text-muted-foreground">
          Payment Analytics Dashboard
        </div>
      </div>

      {/* Analytics Cards */}
      <Suspense fallback={<div className="h-32 w-full bg-muted animate-pulse rounded-lg mb-8"></div>}>
        <PaymentAnalyticsCards analytics={analytics} />
      </Suspense>

      {/* Payment Sections */}
      <div className="space-y-8">
        {/* Regular Successful Payments Section */}
        <div>
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                <CardTitle>Localbites Successful Payments</CardTitle>
              </div>
              <CardDescription>
                All successful payments excluding product purchases ({analytics.regularPayments.count} payments)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Count</div>
                  <div className="text-2xl font-bold">{analytics.regularPayments.count}</div>
                </div>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.regularPayments.amount)}
                  </div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Average Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.regularPayments.count > 0 ? analytics.regularPayments.amount / analytics.regularPayments.count : 0)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Product Purchase Payments Section */}
        {/* <div>
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-orange-600" />
                <CardTitle>Product Purchase Payments</CardTitle>
              </div>
              <CardDescription>
                Payments where the reference contains "product purchase" ({analytics.productPurchasePayments.count} payments)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-orange-50 dark:bg-orange-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Count</div>
                  <div className="text-2xl font-bold">{analytics.productPurchasePayments.count}</div>
                </div>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.productPurchasePayments.amount)}
                  </div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Average Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.productPurchasePayments.count > 0 ? analytics.productPurchasePayments.amount / analytics.productPurchasePayments.count : 0)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}

        {/* Total Service Fees Section */}
        <div>
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-emerald-600" />
                <CardTitle>Total Localbites Service Fees</CardTitle>
              </div>
              <CardDescription>
                Service fees calculated at 8% of payment amount from successful orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-emerald-50 dark:bg-emerald-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Applicable Payments</div>
                  <div className="text-2xl font-bold">{analytics.serviceFees.applicablePayments}</div>
                </div>
                <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total Service Fees</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.serviceFees.totalFees)}
                  </div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Applicable Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.serviceFees.applicableAmount)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* LocalBites (NEXT GEN) Payments Section */}
        {/* <div>
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <CardTitle>LocalBites (NEXT GEN) Payments</CardTitle>
              </div>
              <CardDescription>
                Breakdown of payments from LocalBites platform (source = 'NEXT GEN')
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">NEXT GEN Payments</div>
                  <div className="text-2xl font-bold">{analytics.localBitesPayments.count}</div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
                  <div className="text-sm text-muted-foreground">Total NEXT GEN Amount</div>
                  <div className="text-2xl font-bold">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'LSL',
                      minimumFractionDigits: 2,
                    }).format(analytics.localBitesPayments.amount)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}

        {/* Payments Table */}
        <div>
          <Suspense fallback={<div className="h-96 w-full bg-muted animate-pulse rounded-lg"></div>}>
            <SuccessfulPaymentsTable payments={payments} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
