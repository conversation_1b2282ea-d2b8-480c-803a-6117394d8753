import { Suspense } from 'react';
import { getDevNotificationStats, getDevNotifications } from '@/actions/getDevNotificationsActions';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import StatCards from '@/components/dashboard/StatCards';
import DashboardClient from '@/components/dashboard/DashboardClient';
import { getPaymentMethodAvailability } from '@/actions/paymentMethodAvailabilityActions';

export default async function DashboardPage() {
  // Fetch initial data
  // const { notifications, total } = await getDevNotifications(10, 0);
  // const stats = await getDevNotificationStats();

  const [{notifications, total}, stats, mpesaAvailability, ecocashAvailability] = await Promise.all([
    getDevNotifications(10, 0),
    getDevNotificationStats(),
    getPaymentMethodAvailability("MPESA"),
    getPaymentMethodAvailability("ECOCASH"),
  ]);

  return (
    <div className="container mx-auto px-4 py-8">
      <DashboardHeader />

      <Suspense fallback={<div className="h-24 w-full bg-muted animate-pulse rounded-lg"></div>}>
        <StatCards stats={stats} mpesaAvailability={mpesaAvailability[0]} ecocashAvailability={ecocashAvailability[0]} />
      </Suspense>

      <div className="mt-8">
        <Suspense fallback={<div className="h-96 w-full bg-muted animate-pulse rounded-lg"></div>}>
          <DashboardClient initialData={notifications} totalItems={total} />
        </Suspense>
      </div>
    </div>
  );
}
