export default function DashboardLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="h-8 w-64 bg-muted animate-pulse rounded-lg mb-8"></div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-32 bg-muted animate-pulse rounded-lg"></div>
        ))}
      </div>
      
      <div className="h-16 w-full bg-muted animate-pulse rounded-lg mb-6"></div>
      
      <div className="h-96 w-full bg-muted animate-pulse rounded-lg"></div>
    </div>
  );
}
