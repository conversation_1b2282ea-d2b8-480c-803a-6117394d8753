import { PrismaClient } from '@/lib/generated/prisma';

// Use a single instance of PrismaClient across the application
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Check if we're in a production environment
const isProd = process.env.NODE_ENV === 'production';

// Initialize PrismaClient with error logging options
function makePrisma() {
  try {
    return new PrismaClient({
      log: isProd ? ['error'] : ['query', 'error', 'warn'],
    });
  } catch (error) {
    console.error('Failed to initialize Prisma client:', error);
    throw error;
  }
}

// Use existing Prisma instance if available, otherwise create a new one
export const prisma = globalForPrisma.prisma || makePrisma();

// Save the client instance in development to prevent multiple instances
if (!isProd) globalForPrisma.prisma = prisma;
