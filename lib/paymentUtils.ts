// Utility functions for payment processing
// These are not server actions, so they don't need 'use server' directive

export interface Order {
  id: string;
  total_payable: number;
  source?: string;
  payment_status?: string;
  status?: string;
  active_status?: string;
  order_items_snapshot?: any;
  payment_method?: string;
  created_at?: string;
  date_added?: string;
  delivery_charge?: number;
  rider_id?: string;
  reference?: string;
  merchant_id?: string;
  merchant_name?: string;
}

/**
 * Checks if an order is a product purchase
 * @param {Order} order - The order to check
 * @returns {boolean} True if it's a product purchase
 */
export function isProductPurchase(order: Order): boolean {
  // Check if reference contains "product purchase" (case-insensitive)
  if (order.reference && order.reference.toLowerCase().includes('product purchase')) {
    return true;
  }
  
  // For now, we'll focus on the reference field check since JSON parsing is complex
  // Future enhancement: implement proper JSON parsing for order_items_snapshot
  // when we have cleaner data or better parsing logic
  
  return false;
}

/**
 * Checks if an order is a LocalBites payment (NEXT GEN)
 * @param {Order} order - The order to check
 * @returns {boolean} True if it's a LocalBites payment
 */
export function isLocalBitesPayment(order: Order): boolean {
  return order.source === 'NEXT GEN';
}

/**
 * Formats payment type for display
 * @param {string} paymentType - The payment type to format
 * @returns {string} Formatted payment type
 */
export function formatPaymentType(paymentType: string): string {
  if (!paymentType) return 'Unknown';
  
  switch (paymentType.toUpperCase()) {
    case 'MPESA':
      return 'M-Pesa';
    case 'ECOCASH':
      return 'EcoCash';
    default:
      return paymentType;
  }
}
