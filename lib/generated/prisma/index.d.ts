
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model DevNotification
 * 
 */
export type DevNotification = $Result.DefaultSelection<Prisma.$DevNotificationPayload>
/**
 * Model EarlyAccessUsers
 * 
 */
export type EarlyAccessUsers = $Result.DefaultSelection<Prisma.$EarlyAccessUsersPayload>
/**
 * Model RegisteredRestaurants
 * 
 */
export type RegisteredRestaurants = $Result.DefaultSelection<Prisma.$RegisteredRestaurantsPayload>
/**
 * Model Restaurants
 * 
 */
export type Restaurants = $Result.DefaultSelection<Prisma.$RestaurantsPayload>
/**
 * Model Users
 * 
 */
export type Users = $Result.DefaultSelection<Prisma.$UsersPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more DevNotifications
 * const devNotifications = await prisma.devNotification.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more DevNotifications
   * const devNotifications = await prisma.devNotification.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.devNotification`: Exposes CRUD operations for the **DevNotification** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more DevNotifications
    * const devNotifications = await prisma.devNotification.findMany()
    * ```
    */
  get devNotification(): Prisma.DevNotificationDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.earlyAccessUsers`: Exposes CRUD operations for the **EarlyAccessUsers** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more EarlyAccessUsers
    * const earlyAccessUsers = await prisma.earlyAccessUsers.findMany()
    * ```
    */
  get earlyAccessUsers(): Prisma.EarlyAccessUsersDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.registeredRestaurants`: Exposes CRUD operations for the **RegisteredRestaurants** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more RegisteredRestaurants
    * const registeredRestaurants = await prisma.registeredRestaurants.findMany()
    * ```
    */
  get registeredRestaurants(): Prisma.RegisteredRestaurantsDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.restaurants`: Exposes CRUD operations for the **Restaurants** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Restaurants
    * const restaurants = await prisma.restaurants.findMany()
    * ```
    */
  get restaurants(): Prisma.RestaurantsDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.users`: Exposes CRUD operations for the **Users** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.users.findMany()
    * ```
    */
  get users(): Prisma.UsersDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.6.0
   * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    DevNotification: 'DevNotification',
    EarlyAccessUsers: 'EarlyAccessUsers',
    RegisteredRestaurants: 'RegisteredRestaurants',
    Restaurants: 'Restaurants',
    Users: 'Users'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "devNotification" | "earlyAccessUsers" | "registeredRestaurants" | "restaurants" | "users"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      DevNotification: {
        payload: Prisma.$DevNotificationPayload<ExtArgs>
        fields: Prisma.DevNotificationFieldRefs
        operations: {
          findUnique: {
            args: Prisma.DevNotificationFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.DevNotificationFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          findFirst: {
            args: Prisma.DevNotificationFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.DevNotificationFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          findMany: {
            args: Prisma.DevNotificationFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>[]
          }
          create: {
            args: Prisma.DevNotificationCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          createMany: {
            args: Prisma.DevNotificationCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.DevNotificationCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>[]
          }
          delete: {
            args: Prisma.DevNotificationDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          update: {
            args: Prisma.DevNotificationUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          deleteMany: {
            args: Prisma.DevNotificationDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.DevNotificationUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.DevNotificationUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>[]
          }
          upsert: {
            args: Prisma.DevNotificationUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DevNotificationPayload>
          }
          aggregate: {
            args: Prisma.DevNotificationAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateDevNotification>
          }
          groupBy: {
            args: Prisma.DevNotificationGroupByArgs<ExtArgs>
            result: $Utils.Optional<DevNotificationGroupByOutputType>[]
          }
          count: {
            args: Prisma.DevNotificationCountArgs<ExtArgs>
            result: $Utils.Optional<DevNotificationCountAggregateOutputType> | number
          }
        }
      }
      EarlyAccessUsers: {
        payload: Prisma.$EarlyAccessUsersPayload<ExtArgs>
        fields: Prisma.EarlyAccessUsersFieldRefs
        operations: {
          findUnique: {
            args: Prisma.EarlyAccessUsersFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.EarlyAccessUsersFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          findFirst: {
            args: Prisma.EarlyAccessUsersFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.EarlyAccessUsersFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          findMany: {
            args: Prisma.EarlyAccessUsersFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>[]
          }
          create: {
            args: Prisma.EarlyAccessUsersCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          createMany: {
            args: Prisma.EarlyAccessUsersCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.EarlyAccessUsersCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>[]
          }
          delete: {
            args: Prisma.EarlyAccessUsersDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          update: {
            args: Prisma.EarlyAccessUsersUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          deleteMany: {
            args: Prisma.EarlyAccessUsersDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.EarlyAccessUsersUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.EarlyAccessUsersUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>[]
          }
          upsert: {
            args: Prisma.EarlyAccessUsersUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$EarlyAccessUsersPayload>
          }
          aggregate: {
            args: Prisma.EarlyAccessUsersAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateEarlyAccessUsers>
          }
          groupBy: {
            args: Prisma.EarlyAccessUsersGroupByArgs<ExtArgs>
            result: $Utils.Optional<EarlyAccessUsersGroupByOutputType>[]
          }
          count: {
            args: Prisma.EarlyAccessUsersCountArgs<ExtArgs>
            result: $Utils.Optional<EarlyAccessUsersCountAggregateOutputType> | number
          }
        }
      }
      RegisteredRestaurants: {
        payload: Prisma.$RegisteredRestaurantsPayload<ExtArgs>
        fields: Prisma.RegisteredRestaurantsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RegisteredRestaurantsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RegisteredRestaurantsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          findFirst: {
            args: Prisma.RegisteredRestaurantsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RegisteredRestaurantsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          findMany: {
            args: Prisma.RegisteredRestaurantsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>[]
          }
          create: {
            args: Prisma.RegisteredRestaurantsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          createMany: {
            args: Prisma.RegisteredRestaurantsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RegisteredRestaurantsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>[]
          }
          delete: {
            args: Prisma.RegisteredRestaurantsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          update: {
            args: Prisma.RegisteredRestaurantsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          deleteMany: {
            args: Prisma.RegisteredRestaurantsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RegisteredRestaurantsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.RegisteredRestaurantsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>[]
          }
          upsert: {
            args: Prisma.RegisteredRestaurantsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RegisteredRestaurantsPayload>
          }
          aggregate: {
            args: Prisma.RegisteredRestaurantsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRegisteredRestaurants>
          }
          groupBy: {
            args: Prisma.RegisteredRestaurantsGroupByArgs<ExtArgs>
            result: $Utils.Optional<RegisteredRestaurantsGroupByOutputType>[]
          }
          count: {
            args: Prisma.RegisteredRestaurantsCountArgs<ExtArgs>
            result: $Utils.Optional<RegisteredRestaurantsCountAggregateOutputType> | number
          }
        }
      }
      Restaurants: {
        payload: Prisma.$RestaurantsPayload<ExtArgs>
        fields: Prisma.RestaurantsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.RestaurantsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.RestaurantsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          findFirst: {
            args: Prisma.RestaurantsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.RestaurantsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          findMany: {
            args: Prisma.RestaurantsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>[]
          }
          create: {
            args: Prisma.RestaurantsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          createMany: {
            args: Prisma.RestaurantsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.RestaurantsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>[]
          }
          delete: {
            args: Prisma.RestaurantsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          update: {
            args: Prisma.RestaurantsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          deleteMany: {
            args: Prisma.RestaurantsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.RestaurantsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.RestaurantsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>[]
          }
          upsert: {
            args: Prisma.RestaurantsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$RestaurantsPayload>
          }
          aggregate: {
            args: Prisma.RestaurantsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateRestaurants>
          }
          groupBy: {
            args: Prisma.RestaurantsGroupByArgs<ExtArgs>
            result: $Utils.Optional<RestaurantsGroupByOutputType>[]
          }
          count: {
            args: Prisma.RestaurantsCountArgs<ExtArgs>
            result: $Utils.Optional<RestaurantsCountAggregateOutputType> | number
          }
        }
      }
      Users: {
        payload: Prisma.$UsersPayload<ExtArgs>
        fields: Prisma.UsersFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UsersFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UsersFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          findFirst: {
            args: Prisma.UsersFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UsersFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          findMany: {
            args: Prisma.UsersFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>[]
          }
          create: {
            args: Prisma.UsersCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          createMany: {
            args: Prisma.UsersCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UsersCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>[]
          }
          delete: {
            args: Prisma.UsersDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          update: {
            args: Prisma.UsersUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          deleteMany: {
            args: Prisma.UsersDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UsersUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UsersUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>[]
          }
          upsert: {
            args: Prisma.UsersUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UsersPayload>
          }
          aggregate: {
            args: Prisma.UsersAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUsers>
          }
          groupBy: {
            args: Prisma.UsersGroupByArgs<ExtArgs>
            result: $Utils.Optional<UsersGroupByOutputType>[]
          }
          count: {
            args: Prisma.UsersCountArgs<ExtArgs>
            result: $Utils.Optional<UsersCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    devNotification?: DevNotificationOmit
    earlyAccessUsers?: EarlyAccessUsersOmit
    registeredRestaurants?: RegisteredRestaurantsOmit
    restaurants?: RestaurantsOmit
    users?: UsersOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model DevNotification
   */

  export type AggregateDevNotification = {
    _count: DevNotificationCountAggregateOutputType | null
    _avg: DevNotificationAvgAggregateOutputType | null
    _sum: DevNotificationSumAggregateOutputType | null
    _min: DevNotificationMinAggregateOutputType | null
    _max: DevNotificationMaxAggregateOutputType | null
  }

  export type DevNotificationAvgAggregateOutputType = {
    amount: Decimal | null
  }

  export type DevNotificationSumAggregateOutputType = {
    amount: Decimal | null
  }

  export type DevNotificationMinAggregateOutputType = {
    id: string | null
    endpoint: string | null
    merchantId: string | null
    merchantName: string | null
    amount: Decimal | null
    mobileNumber: string | null
    clientReference: string | null
    paymentType: string | null
    timestamp: Date | null
  }

  export type DevNotificationMaxAggregateOutputType = {
    id: string | null
    endpoint: string | null
    merchantId: string | null
    merchantName: string | null
    amount: Decimal | null
    mobileNumber: string | null
    clientReference: string | null
    paymentType: string | null
    timestamp: Date | null
  }

  export type DevNotificationCountAggregateOutputType = {
    id: number
    endpoint: number
    merchantId: number
    merchantName: number
    amount: number
    mobileNumber: number
    clientReference: number
    paymentType: number
    timestamp: number
    _all: number
  }


  export type DevNotificationAvgAggregateInputType = {
    amount?: true
  }

  export type DevNotificationSumAggregateInputType = {
    amount?: true
  }

  export type DevNotificationMinAggregateInputType = {
    id?: true
    endpoint?: true
    merchantId?: true
    merchantName?: true
    amount?: true
    mobileNumber?: true
    clientReference?: true
    paymentType?: true
    timestamp?: true
  }

  export type DevNotificationMaxAggregateInputType = {
    id?: true
    endpoint?: true
    merchantId?: true
    merchantName?: true
    amount?: true
    mobileNumber?: true
    clientReference?: true
    paymentType?: true
    timestamp?: true
  }

  export type DevNotificationCountAggregateInputType = {
    id?: true
    endpoint?: true
    merchantId?: true
    merchantName?: true
    amount?: true
    mobileNumber?: true
    clientReference?: true
    paymentType?: true
    timestamp?: true
    _all?: true
  }

  export type DevNotificationAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DevNotification to aggregate.
     */
    where?: DevNotificationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DevNotifications to fetch.
     */
    orderBy?: DevNotificationOrderByWithRelationInput | DevNotificationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: DevNotificationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DevNotifications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DevNotifications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned DevNotifications
    **/
    _count?: true | DevNotificationCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: DevNotificationAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: DevNotificationSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: DevNotificationMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: DevNotificationMaxAggregateInputType
  }

  export type GetDevNotificationAggregateType<T extends DevNotificationAggregateArgs> = {
        [P in keyof T & keyof AggregateDevNotification]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateDevNotification[P]>
      : GetScalarType<T[P], AggregateDevNotification[P]>
  }




  export type DevNotificationGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DevNotificationWhereInput
    orderBy?: DevNotificationOrderByWithAggregationInput | DevNotificationOrderByWithAggregationInput[]
    by: DevNotificationScalarFieldEnum[] | DevNotificationScalarFieldEnum
    having?: DevNotificationScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: DevNotificationCountAggregateInputType | true
    _avg?: DevNotificationAvgAggregateInputType
    _sum?: DevNotificationSumAggregateInputType
    _min?: DevNotificationMinAggregateInputType
    _max?: DevNotificationMaxAggregateInputType
  }

  export type DevNotificationGroupByOutputType = {
    id: string
    endpoint: string
    merchantId: string
    merchantName: string
    amount: Decimal
    mobileNumber: string
    clientReference: string
    paymentType: string
    timestamp: Date
    _count: DevNotificationCountAggregateOutputType | null
    _avg: DevNotificationAvgAggregateOutputType | null
    _sum: DevNotificationSumAggregateOutputType | null
    _min: DevNotificationMinAggregateOutputType | null
    _max: DevNotificationMaxAggregateOutputType | null
  }

  type GetDevNotificationGroupByPayload<T extends DevNotificationGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<DevNotificationGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof DevNotificationGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], DevNotificationGroupByOutputType[P]>
            : GetScalarType<T[P], DevNotificationGroupByOutputType[P]>
        }
      >
    >


  export type DevNotificationSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    endpoint?: boolean
    merchantId?: boolean
    merchantName?: boolean
    amount?: boolean
    mobileNumber?: boolean
    clientReference?: boolean
    paymentType?: boolean
    timestamp?: boolean
  }, ExtArgs["result"]["devNotification"]>

  export type DevNotificationSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    endpoint?: boolean
    merchantId?: boolean
    merchantName?: boolean
    amount?: boolean
    mobileNumber?: boolean
    clientReference?: boolean
    paymentType?: boolean
    timestamp?: boolean
  }, ExtArgs["result"]["devNotification"]>

  export type DevNotificationSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    endpoint?: boolean
    merchantId?: boolean
    merchantName?: boolean
    amount?: boolean
    mobileNumber?: boolean
    clientReference?: boolean
    paymentType?: boolean
    timestamp?: boolean
  }, ExtArgs["result"]["devNotification"]>

  export type DevNotificationSelectScalar = {
    id?: boolean
    endpoint?: boolean
    merchantId?: boolean
    merchantName?: boolean
    amount?: boolean
    mobileNumber?: boolean
    clientReference?: boolean
    paymentType?: boolean
    timestamp?: boolean
  }

  export type DevNotificationOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "endpoint" | "merchantId" | "merchantName" | "amount" | "mobileNumber" | "clientReference" | "paymentType" | "timestamp", ExtArgs["result"]["devNotification"]>

  export type $DevNotificationPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "DevNotification"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      endpoint: string
      merchantId: string
      merchantName: string
      amount: Prisma.Decimal
      mobileNumber: string
      clientReference: string
      paymentType: string
      timestamp: Date
    }, ExtArgs["result"]["devNotification"]>
    composites: {}
  }

  type DevNotificationGetPayload<S extends boolean | null | undefined | DevNotificationDefaultArgs> = $Result.GetResult<Prisma.$DevNotificationPayload, S>

  type DevNotificationCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<DevNotificationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: DevNotificationCountAggregateInputType | true
    }

  export interface DevNotificationDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['DevNotification'], meta: { name: 'DevNotification' } }
    /**
     * Find zero or one DevNotification that matches the filter.
     * @param {DevNotificationFindUniqueArgs} args - Arguments to find a DevNotification
     * @example
     * // Get one DevNotification
     * const devNotification = await prisma.devNotification.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends DevNotificationFindUniqueArgs>(args: SelectSubset<T, DevNotificationFindUniqueArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one DevNotification that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {DevNotificationFindUniqueOrThrowArgs} args - Arguments to find a DevNotification
     * @example
     * // Get one DevNotification
     * const devNotification = await prisma.devNotification.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends DevNotificationFindUniqueOrThrowArgs>(args: SelectSubset<T, DevNotificationFindUniqueOrThrowArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first DevNotification that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationFindFirstArgs} args - Arguments to find a DevNotification
     * @example
     * // Get one DevNotification
     * const devNotification = await prisma.devNotification.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends DevNotificationFindFirstArgs>(args?: SelectSubset<T, DevNotificationFindFirstArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first DevNotification that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationFindFirstOrThrowArgs} args - Arguments to find a DevNotification
     * @example
     * // Get one DevNotification
     * const devNotification = await prisma.devNotification.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends DevNotificationFindFirstOrThrowArgs>(args?: SelectSubset<T, DevNotificationFindFirstOrThrowArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more DevNotifications that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all DevNotifications
     * const devNotifications = await prisma.devNotification.findMany()
     * 
     * // Get first 10 DevNotifications
     * const devNotifications = await prisma.devNotification.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const devNotificationWithIdOnly = await prisma.devNotification.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends DevNotificationFindManyArgs>(args?: SelectSubset<T, DevNotificationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a DevNotification.
     * @param {DevNotificationCreateArgs} args - Arguments to create a DevNotification.
     * @example
     * // Create one DevNotification
     * const DevNotification = await prisma.devNotification.create({
     *   data: {
     *     // ... data to create a DevNotification
     *   }
     * })
     * 
     */
    create<T extends DevNotificationCreateArgs>(args: SelectSubset<T, DevNotificationCreateArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many DevNotifications.
     * @param {DevNotificationCreateManyArgs} args - Arguments to create many DevNotifications.
     * @example
     * // Create many DevNotifications
     * const devNotification = await prisma.devNotification.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends DevNotificationCreateManyArgs>(args?: SelectSubset<T, DevNotificationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many DevNotifications and returns the data saved in the database.
     * @param {DevNotificationCreateManyAndReturnArgs} args - Arguments to create many DevNotifications.
     * @example
     * // Create many DevNotifications
     * const devNotification = await prisma.devNotification.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many DevNotifications and only return the `id`
     * const devNotificationWithIdOnly = await prisma.devNotification.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends DevNotificationCreateManyAndReturnArgs>(args?: SelectSubset<T, DevNotificationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a DevNotification.
     * @param {DevNotificationDeleteArgs} args - Arguments to delete one DevNotification.
     * @example
     * // Delete one DevNotification
     * const DevNotification = await prisma.devNotification.delete({
     *   where: {
     *     // ... filter to delete one DevNotification
     *   }
     * })
     * 
     */
    delete<T extends DevNotificationDeleteArgs>(args: SelectSubset<T, DevNotificationDeleteArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one DevNotification.
     * @param {DevNotificationUpdateArgs} args - Arguments to update one DevNotification.
     * @example
     * // Update one DevNotification
     * const devNotification = await prisma.devNotification.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends DevNotificationUpdateArgs>(args: SelectSubset<T, DevNotificationUpdateArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more DevNotifications.
     * @param {DevNotificationDeleteManyArgs} args - Arguments to filter DevNotifications to delete.
     * @example
     * // Delete a few DevNotifications
     * const { count } = await prisma.devNotification.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends DevNotificationDeleteManyArgs>(args?: SelectSubset<T, DevNotificationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DevNotifications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many DevNotifications
     * const devNotification = await prisma.devNotification.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends DevNotificationUpdateManyArgs>(args: SelectSubset<T, DevNotificationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DevNotifications and returns the data updated in the database.
     * @param {DevNotificationUpdateManyAndReturnArgs} args - Arguments to update many DevNotifications.
     * @example
     * // Update many DevNotifications
     * const devNotification = await prisma.devNotification.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more DevNotifications and only return the `id`
     * const devNotificationWithIdOnly = await prisma.devNotification.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends DevNotificationUpdateManyAndReturnArgs>(args: SelectSubset<T, DevNotificationUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one DevNotification.
     * @param {DevNotificationUpsertArgs} args - Arguments to update or create a DevNotification.
     * @example
     * // Update or create a DevNotification
     * const devNotification = await prisma.devNotification.upsert({
     *   create: {
     *     // ... data to create a DevNotification
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the DevNotification we want to update
     *   }
     * })
     */
    upsert<T extends DevNotificationUpsertArgs>(args: SelectSubset<T, DevNotificationUpsertArgs<ExtArgs>>): Prisma__DevNotificationClient<$Result.GetResult<Prisma.$DevNotificationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of DevNotifications.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationCountArgs} args - Arguments to filter DevNotifications to count.
     * @example
     * // Count the number of DevNotifications
     * const count = await prisma.devNotification.count({
     *   where: {
     *     // ... the filter for the DevNotifications we want to count
     *   }
     * })
    **/
    count<T extends DevNotificationCountArgs>(
      args?: Subset<T, DevNotificationCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], DevNotificationCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a DevNotification.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends DevNotificationAggregateArgs>(args: Subset<T, DevNotificationAggregateArgs>): Prisma.PrismaPromise<GetDevNotificationAggregateType<T>>

    /**
     * Group by DevNotification.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DevNotificationGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends DevNotificationGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: DevNotificationGroupByArgs['orderBy'] }
        : { orderBy?: DevNotificationGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, DevNotificationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDevNotificationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the DevNotification model
   */
  readonly fields: DevNotificationFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for DevNotification.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__DevNotificationClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the DevNotification model
   */
  interface DevNotificationFieldRefs {
    readonly id: FieldRef<"DevNotification", 'String'>
    readonly endpoint: FieldRef<"DevNotification", 'String'>
    readonly merchantId: FieldRef<"DevNotification", 'String'>
    readonly merchantName: FieldRef<"DevNotification", 'String'>
    readonly amount: FieldRef<"DevNotification", 'Decimal'>
    readonly mobileNumber: FieldRef<"DevNotification", 'String'>
    readonly clientReference: FieldRef<"DevNotification", 'String'>
    readonly paymentType: FieldRef<"DevNotification", 'String'>
    readonly timestamp: FieldRef<"DevNotification", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * DevNotification findUnique
   */
  export type DevNotificationFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter, which DevNotification to fetch.
     */
    where: DevNotificationWhereUniqueInput
  }

  /**
   * DevNotification findUniqueOrThrow
   */
  export type DevNotificationFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter, which DevNotification to fetch.
     */
    where: DevNotificationWhereUniqueInput
  }

  /**
   * DevNotification findFirst
   */
  export type DevNotificationFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter, which DevNotification to fetch.
     */
    where?: DevNotificationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DevNotifications to fetch.
     */
    orderBy?: DevNotificationOrderByWithRelationInput | DevNotificationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DevNotifications.
     */
    cursor?: DevNotificationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DevNotifications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DevNotifications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DevNotifications.
     */
    distinct?: DevNotificationScalarFieldEnum | DevNotificationScalarFieldEnum[]
  }

  /**
   * DevNotification findFirstOrThrow
   */
  export type DevNotificationFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter, which DevNotification to fetch.
     */
    where?: DevNotificationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DevNotifications to fetch.
     */
    orderBy?: DevNotificationOrderByWithRelationInput | DevNotificationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DevNotifications.
     */
    cursor?: DevNotificationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DevNotifications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DevNotifications.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DevNotifications.
     */
    distinct?: DevNotificationScalarFieldEnum | DevNotificationScalarFieldEnum[]
  }

  /**
   * DevNotification findMany
   */
  export type DevNotificationFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter, which DevNotifications to fetch.
     */
    where?: DevNotificationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DevNotifications to fetch.
     */
    orderBy?: DevNotificationOrderByWithRelationInput | DevNotificationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing DevNotifications.
     */
    cursor?: DevNotificationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DevNotifications from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DevNotifications.
     */
    skip?: number
    distinct?: DevNotificationScalarFieldEnum | DevNotificationScalarFieldEnum[]
  }

  /**
   * DevNotification create
   */
  export type DevNotificationCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * The data needed to create a DevNotification.
     */
    data: XOR<DevNotificationCreateInput, DevNotificationUncheckedCreateInput>
  }

  /**
   * DevNotification createMany
   */
  export type DevNotificationCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many DevNotifications.
     */
    data: DevNotificationCreateManyInput | DevNotificationCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * DevNotification createManyAndReturn
   */
  export type DevNotificationCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * The data used to create many DevNotifications.
     */
    data: DevNotificationCreateManyInput | DevNotificationCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * DevNotification update
   */
  export type DevNotificationUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * The data needed to update a DevNotification.
     */
    data: XOR<DevNotificationUpdateInput, DevNotificationUncheckedUpdateInput>
    /**
     * Choose, which DevNotification to update.
     */
    where: DevNotificationWhereUniqueInput
  }

  /**
   * DevNotification updateMany
   */
  export type DevNotificationUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update DevNotifications.
     */
    data: XOR<DevNotificationUpdateManyMutationInput, DevNotificationUncheckedUpdateManyInput>
    /**
     * Filter which DevNotifications to update
     */
    where?: DevNotificationWhereInput
    /**
     * Limit how many DevNotifications to update.
     */
    limit?: number
  }

  /**
   * DevNotification updateManyAndReturn
   */
  export type DevNotificationUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * The data used to update DevNotifications.
     */
    data: XOR<DevNotificationUpdateManyMutationInput, DevNotificationUncheckedUpdateManyInput>
    /**
     * Filter which DevNotifications to update
     */
    where?: DevNotificationWhereInput
    /**
     * Limit how many DevNotifications to update.
     */
    limit?: number
  }

  /**
   * DevNotification upsert
   */
  export type DevNotificationUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * The filter to search for the DevNotification to update in case it exists.
     */
    where: DevNotificationWhereUniqueInput
    /**
     * In case the DevNotification found by the `where` argument doesn't exist, create a new DevNotification with this data.
     */
    create: XOR<DevNotificationCreateInput, DevNotificationUncheckedCreateInput>
    /**
     * In case the DevNotification was found with the provided `where` argument, update it with this data.
     */
    update: XOR<DevNotificationUpdateInput, DevNotificationUncheckedUpdateInput>
  }

  /**
   * DevNotification delete
   */
  export type DevNotificationDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
    /**
     * Filter which DevNotification to delete.
     */
    where: DevNotificationWhereUniqueInput
  }

  /**
   * DevNotification deleteMany
   */
  export type DevNotificationDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DevNotifications to delete
     */
    where?: DevNotificationWhereInput
    /**
     * Limit how many DevNotifications to delete.
     */
    limit?: number
  }

  /**
   * DevNotification without action
   */
  export type DevNotificationDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DevNotification
     */
    select?: DevNotificationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DevNotification
     */
    omit?: DevNotificationOmit<ExtArgs> | null
  }


  /**
   * Model EarlyAccessUsers
   */

  export type AggregateEarlyAccessUsers = {
    _count: EarlyAccessUsersCountAggregateOutputType | null
    _min: EarlyAccessUsersMinAggregateOutputType | null
    _max: EarlyAccessUsersMaxAggregateOutputType | null
  }

  export type EarlyAccessUsersMinAggregateOutputType = {
    userId: string | null
    phoneNumber: string | null
    approved: boolean | null
  }

  export type EarlyAccessUsersMaxAggregateOutputType = {
    userId: string | null
    phoneNumber: string | null
    approved: boolean | null
  }

  export type EarlyAccessUsersCountAggregateOutputType = {
    userId: number
    phoneNumber: number
    socialMediaLinks: number
    approved: number
    _all: number
  }


  export type EarlyAccessUsersMinAggregateInputType = {
    userId?: true
    phoneNumber?: true
    approved?: true
  }

  export type EarlyAccessUsersMaxAggregateInputType = {
    userId?: true
    phoneNumber?: true
    approved?: true
  }

  export type EarlyAccessUsersCountAggregateInputType = {
    userId?: true
    phoneNumber?: true
    socialMediaLinks?: true
    approved?: true
    _all?: true
  }

  export type EarlyAccessUsersAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which EarlyAccessUsers to aggregate.
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of EarlyAccessUsers to fetch.
     */
    orderBy?: EarlyAccessUsersOrderByWithRelationInput | EarlyAccessUsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: EarlyAccessUsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` EarlyAccessUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` EarlyAccessUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned EarlyAccessUsers
    **/
    _count?: true | EarlyAccessUsersCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: EarlyAccessUsersMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: EarlyAccessUsersMaxAggregateInputType
  }

  export type GetEarlyAccessUsersAggregateType<T extends EarlyAccessUsersAggregateArgs> = {
        [P in keyof T & keyof AggregateEarlyAccessUsers]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateEarlyAccessUsers[P]>
      : GetScalarType<T[P], AggregateEarlyAccessUsers[P]>
  }




  export type EarlyAccessUsersGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: EarlyAccessUsersWhereInput
    orderBy?: EarlyAccessUsersOrderByWithAggregationInput | EarlyAccessUsersOrderByWithAggregationInput[]
    by: EarlyAccessUsersScalarFieldEnum[] | EarlyAccessUsersScalarFieldEnum
    having?: EarlyAccessUsersScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: EarlyAccessUsersCountAggregateInputType | true
    _min?: EarlyAccessUsersMinAggregateInputType
    _max?: EarlyAccessUsersMaxAggregateInputType
  }

  export type EarlyAccessUsersGroupByOutputType = {
    userId: string
    phoneNumber: string
    socialMediaLinks: string[]
    approved: boolean
    _count: EarlyAccessUsersCountAggregateOutputType | null
    _min: EarlyAccessUsersMinAggregateOutputType | null
    _max: EarlyAccessUsersMaxAggregateOutputType | null
  }

  type GetEarlyAccessUsersGroupByPayload<T extends EarlyAccessUsersGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<EarlyAccessUsersGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof EarlyAccessUsersGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], EarlyAccessUsersGroupByOutputType[P]>
            : GetScalarType<T[P], EarlyAccessUsersGroupByOutputType[P]>
        }
      >
    >


  export type EarlyAccessUsersSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    phoneNumber?: boolean
    socialMediaLinks?: boolean
    approved?: boolean
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["earlyAccessUsers"]>

  export type EarlyAccessUsersSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    phoneNumber?: boolean
    socialMediaLinks?: boolean
    approved?: boolean
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["earlyAccessUsers"]>

  export type EarlyAccessUsersSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    phoneNumber?: boolean
    socialMediaLinks?: boolean
    approved?: boolean
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["earlyAccessUsers"]>

  export type EarlyAccessUsersSelectScalar = {
    userId?: boolean
    phoneNumber?: boolean
    socialMediaLinks?: boolean
    approved?: boolean
  }

  export type EarlyAccessUsersOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"userId" | "phoneNumber" | "socialMediaLinks" | "approved", ExtArgs["result"]["earlyAccessUsers"]>
  export type EarlyAccessUsersInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }
  export type EarlyAccessUsersIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }
  export type EarlyAccessUsersIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    Users?: boolean | UsersDefaultArgs<ExtArgs>
  }

  export type $EarlyAccessUsersPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "EarlyAccessUsers"
    objects: {
      Users: Prisma.$UsersPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      userId: string
      phoneNumber: string
      socialMediaLinks: string[]
      approved: boolean
    }, ExtArgs["result"]["earlyAccessUsers"]>
    composites: {}
  }

  type EarlyAccessUsersGetPayload<S extends boolean | null | undefined | EarlyAccessUsersDefaultArgs> = $Result.GetResult<Prisma.$EarlyAccessUsersPayload, S>

  type EarlyAccessUsersCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<EarlyAccessUsersFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: EarlyAccessUsersCountAggregateInputType | true
    }

  export interface EarlyAccessUsersDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['EarlyAccessUsers'], meta: { name: 'EarlyAccessUsers' } }
    /**
     * Find zero or one EarlyAccessUsers that matches the filter.
     * @param {EarlyAccessUsersFindUniqueArgs} args - Arguments to find a EarlyAccessUsers
     * @example
     * // Get one EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends EarlyAccessUsersFindUniqueArgs>(args: SelectSubset<T, EarlyAccessUsersFindUniqueArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one EarlyAccessUsers that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {EarlyAccessUsersFindUniqueOrThrowArgs} args - Arguments to find a EarlyAccessUsers
     * @example
     * // Get one EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends EarlyAccessUsersFindUniqueOrThrowArgs>(args: SelectSubset<T, EarlyAccessUsersFindUniqueOrThrowArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first EarlyAccessUsers that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersFindFirstArgs} args - Arguments to find a EarlyAccessUsers
     * @example
     * // Get one EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends EarlyAccessUsersFindFirstArgs>(args?: SelectSubset<T, EarlyAccessUsersFindFirstArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first EarlyAccessUsers that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersFindFirstOrThrowArgs} args - Arguments to find a EarlyAccessUsers
     * @example
     * // Get one EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends EarlyAccessUsersFindFirstOrThrowArgs>(args?: SelectSubset<T, EarlyAccessUsersFindFirstOrThrowArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more EarlyAccessUsers that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findMany()
     * 
     * // Get first 10 EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.findMany({ take: 10 })
     * 
     * // Only select the `userId`
     * const earlyAccessUsersWithUserIdOnly = await prisma.earlyAccessUsers.findMany({ select: { userId: true } })
     * 
     */
    findMany<T extends EarlyAccessUsersFindManyArgs>(args?: SelectSubset<T, EarlyAccessUsersFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a EarlyAccessUsers.
     * @param {EarlyAccessUsersCreateArgs} args - Arguments to create a EarlyAccessUsers.
     * @example
     * // Create one EarlyAccessUsers
     * const EarlyAccessUsers = await prisma.earlyAccessUsers.create({
     *   data: {
     *     // ... data to create a EarlyAccessUsers
     *   }
     * })
     * 
     */
    create<T extends EarlyAccessUsersCreateArgs>(args: SelectSubset<T, EarlyAccessUsersCreateArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many EarlyAccessUsers.
     * @param {EarlyAccessUsersCreateManyArgs} args - Arguments to create many EarlyAccessUsers.
     * @example
     * // Create many EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends EarlyAccessUsersCreateManyArgs>(args?: SelectSubset<T, EarlyAccessUsersCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many EarlyAccessUsers and returns the data saved in the database.
     * @param {EarlyAccessUsersCreateManyAndReturnArgs} args - Arguments to create many EarlyAccessUsers.
     * @example
     * // Create many EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many EarlyAccessUsers and only return the `userId`
     * const earlyAccessUsersWithUserIdOnly = await prisma.earlyAccessUsers.createManyAndReturn({
     *   select: { userId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends EarlyAccessUsersCreateManyAndReturnArgs>(args?: SelectSubset<T, EarlyAccessUsersCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a EarlyAccessUsers.
     * @param {EarlyAccessUsersDeleteArgs} args - Arguments to delete one EarlyAccessUsers.
     * @example
     * // Delete one EarlyAccessUsers
     * const EarlyAccessUsers = await prisma.earlyAccessUsers.delete({
     *   where: {
     *     // ... filter to delete one EarlyAccessUsers
     *   }
     * })
     * 
     */
    delete<T extends EarlyAccessUsersDeleteArgs>(args: SelectSubset<T, EarlyAccessUsersDeleteArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one EarlyAccessUsers.
     * @param {EarlyAccessUsersUpdateArgs} args - Arguments to update one EarlyAccessUsers.
     * @example
     * // Update one EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends EarlyAccessUsersUpdateArgs>(args: SelectSubset<T, EarlyAccessUsersUpdateArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more EarlyAccessUsers.
     * @param {EarlyAccessUsersDeleteManyArgs} args - Arguments to filter EarlyAccessUsers to delete.
     * @example
     * // Delete a few EarlyAccessUsers
     * const { count } = await prisma.earlyAccessUsers.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends EarlyAccessUsersDeleteManyArgs>(args?: SelectSubset<T, EarlyAccessUsersDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more EarlyAccessUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends EarlyAccessUsersUpdateManyArgs>(args: SelectSubset<T, EarlyAccessUsersUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more EarlyAccessUsers and returns the data updated in the database.
     * @param {EarlyAccessUsersUpdateManyAndReturnArgs} args - Arguments to update many EarlyAccessUsers.
     * @example
     * // Update many EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more EarlyAccessUsers and only return the `userId`
     * const earlyAccessUsersWithUserIdOnly = await prisma.earlyAccessUsers.updateManyAndReturn({
     *   select: { userId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends EarlyAccessUsersUpdateManyAndReturnArgs>(args: SelectSubset<T, EarlyAccessUsersUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one EarlyAccessUsers.
     * @param {EarlyAccessUsersUpsertArgs} args - Arguments to update or create a EarlyAccessUsers.
     * @example
     * // Update or create a EarlyAccessUsers
     * const earlyAccessUsers = await prisma.earlyAccessUsers.upsert({
     *   create: {
     *     // ... data to create a EarlyAccessUsers
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the EarlyAccessUsers we want to update
     *   }
     * })
     */
    upsert<T extends EarlyAccessUsersUpsertArgs>(args: SelectSubset<T, EarlyAccessUsersUpsertArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of EarlyAccessUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersCountArgs} args - Arguments to filter EarlyAccessUsers to count.
     * @example
     * // Count the number of EarlyAccessUsers
     * const count = await prisma.earlyAccessUsers.count({
     *   where: {
     *     // ... the filter for the EarlyAccessUsers we want to count
     *   }
     * })
    **/
    count<T extends EarlyAccessUsersCountArgs>(
      args?: Subset<T, EarlyAccessUsersCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], EarlyAccessUsersCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a EarlyAccessUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends EarlyAccessUsersAggregateArgs>(args: Subset<T, EarlyAccessUsersAggregateArgs>): Prisma.PrismaPromise<GetEarlyAccessUsersAggregateType<T>>

    /**
     * Group by EarlyAccessUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {EarlyAccessUsersGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends EarlyAccessUsersGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: EarlyAccessUsersGroupByArgs['orderBy'] }
        : { orderBy?: EarlyAccessUsersGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, EarlyAccessUsersGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetEarlyAccessUsersGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the EarlyAccessUsers model
   */
  readonly fields: EarlyAccessUsersFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for EarlyAccessUsers.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__EarlyAccessUsersClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    Users<T extends UsersDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UsersDefaultArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the EarlyAccessUsers model
   */
  interface EarlyAccessUsersFieldRefs {
    readonly userId: FieldRef<"EarlyAccessUsers", 'String'>
    readonly phoneNumber: FieldRef<"EarlyAccessUsers", 'String'>
    readonly socialMediaLinks: FieldRef<"EarlyAccessUsers", 'String[]'>
    readonly approved: FieldRef<"EarlyAccessUsers", 'Boolean'>
  }
    

  // Custom InputTypes
  /**
   * EarlyAccessUsers findUnique
   */
  export type EarlyAccessUsersFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter, which EarlyAccessUsers to fetch.
     */
    where: EarlyAccessUsersWhereUniqueInput
  }

  /**
   * EarlyAccessUsers findUniqueOrThrow
   */
  export type EarlyAccessUsersFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter, which EarlyAccessUsers to fetch.
     */
    where: EarlyAccessUsersWhereUniqueInput
  }

  /**
   * EarlyAccessUsers findFirst
   */
  export type EarlyAccessUsersFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter, which EarlyAccessUsers to fetch.
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of EarlyAccessUsers to fetch.
     */
    orderBy?: EarlyAccessUsersOrderByWithRelationInput | EarlyAccessUsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for EarlyAccessUsers.
     */
    cursor?: EarlyAccessUsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` EarlyAccessUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` EarlyAccessUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of EarlyAccessUsers.
     */
    distinct?: EarlyAccessUsersScalarFieldEnum | EarlyAccessUsersScalarFieldEnum[]
  }

  /**
   * EarlyAccessUsers findFirstOrThrow
   */
  export type EarlyAccessUsersFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter, which EarlyAccessUsers to fetch.
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of EarlyAccessUsers to fetch.
     */
    orderBy?: EarlyAccessUsersOrderByWithRelationInput | EarlyAccessUsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for EarlyAccessUsers.
     */
    cursor?: EarlyAccessUsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` EarlyAccessUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` EarlyAccessUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of EarlyAccessUsers.
     */
    distinct?: EarlyAccessUsersScalarFieldEnum | EarlyAccessUsersScalarFieldEnum[]
  }

  /**
   * EarlyAccessUsers findMany
   */
  export type EarlyAccessUsersFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter, which EarlyAccessUsers to fetch.
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of EarlyAccessUsers to fetch.
     */
    orderBy?: EarlyAccessUsersOrderByWithRelationInput | EarlyAccessUsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing EarlyAccessUsers.
     */
    cursor?: EarlyAccessUsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` EarlyAccessUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` EarlyAccessUsers.
     */
    skip?: number
    distinct?: EarlyAccessUsersScalarFieldEnum | EarlyAccessUsersScalarFieldEnum[]
  }

  /**
   * EarlyAccessUsers create
   */
  export type EarlyAccessUsersCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * The data needed to create a EarlyAccessUsers.
     */
    data: XOR<EarlyAccessUsersCreateInput, EarlyAccessUsersUncheckedCreateInput>
  }

  /**
   * EarlyAccessUsers createMany
   */
  export type EarlyAccessUsersCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many EarlyAccessUsers.
     */
    data: EarlyAccessUsersCreateManyInput | EarlyAccessUsersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * EarlyAccessUsers createManyAndReturn
   */
  export type EarlyAccessUsersCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * The data used to create many EarlyAccessUsers.
     */
    data: EarlyAccessUsersCreateManyInput | EarlyAccessUsersCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * EarlyAccessUsers update
   */
  export type EarlyAccessUsersUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * The data needed to update a EarlyAccessUsers.
     */
    data: XOR<EarlyAccessUsersUpdateInput, EarlyAccessUsersUncheckedUpdateInput>
    /**
     * Choose, which EarlyAccessUsers to update.
     */
    where: EarlyAccessUsersWhereUniqueInput
  }

  /**
   * EarlyAccessUsers updateMany
   */
  export type EarlyAccessUsersUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update EarlyAccessUsers.
     */
    data: XOR<EarlyAccessUsersUpdateManyMutationInput, EarlyAccessUsersUncheckedUpdateManyInput>
    /**
     * Filter which EarlyAccessUsers to update
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * Limit how many EarlyAccessUsers to update.
     */
    limit?: number
  }

  /**
   * EarlyAccessUsers updateManyAndReturn
   */
  export type EarlyAccessUsersUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * The data used to update EarlyAccessUsers.
     */
    data: XOR<EarlyAccessUsersUpdateManyMutationInput, EarlyAccessUsersUncheckedUpdateManyInput>
    /**
     * Filter which EarlyAccessUsers to update
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * Limit how many EarlyAccessUsers to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * EarlyAccessUsers upsert
   */
  export type EarlyAccessUsersUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * The filter to search for the EarlyAccessUsers to update in case it exists.
     */
    where: EarlyAccessUsersWhereUniqueInput
    /**
     * In case the EarlyAccessUsers found by the `where` argument doesn't exist, create a new EarlyAccessUsers with this data.
     */
    create: XOR<EarlyAccessUsersCreateInput, EarlyAccessUsersUncheckedCreateInput>
    /**
     * In case the EarlyAccessUsers was found with the provided `where` argument, update it with this data.
     */
    update: XOR<EarlyAccessUsersUpdateInput, EarlyAccessUsersUncheckedUpdateInput>
  }

  /**
   * EarlyAccessUsers delete
   */
  export type EarlyAccessUsersDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    /**
     * Filter which EarlyAccessUsers to delete.
     */
    where: EarlyAccessUsersWhereUniqueInput
  }

  /**
   * EarlyAccessUsers deleteMany
   */
  export type EarlyAccessUsersDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which EarlyAccessUsers to delete
     */
    where?: EarlyAccessUsersWhereInput
    /**
     * Limit how many EarlyAccessUsers to delete.
     */
    limit?: number
  }

  /**
   * EarlyAccessUsers without action
   */
  export type EarlyAccessUsersDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
  }


  /**
   * Model RegisteredRestaurants
   */

  export type AggregateRegisteredRestaurants = {
    _count: RegisteredRestaurantsCountAggregateOutputType | null
    _min: RegisteredRestaurantsMinAggregateOutputType | null
    _max: RegisteredRestaurantsMaxAggregateOutputType | null
  }

  export type RegisteredRestaurantsMinAggregateOutputType = {
    restaurantId: string | null
    name: string | null
    owner: string | null
    email: string | null
    phone: string | null
    restaurantType: string | null
    additionalInfo: string | null
  }

  export type RegisteredRestaurantsMaxAggregateOutputType = {
    restaurantId: string | null
    name: string | null
    owner: string | null
    email: string | null
    phone: string | null
    restaurantType: string | null
    additionalInfo: string | null
  }

  export type RegisteredRestaurantsCountAggregateOutputType = {
    restaurantId: number
    name: number
    owner: number
    email: number
    phone: number
    restaurantType: number
    additionalInfo: number
    _all: number
  }


  export type RegisteredRestaurantsMinAggregateInputType = {
    restaurantId?: true
    name?: true
    owner?: true
    email?: true
    phone?: true
    restaurantType?: true
    additionalInfo?: true
  }

  export type RegisteredRestaurantsMaxAggregateInputType = {
    restaurantId?: true
    name?: true
    owner?: true
    email?: true
    phone?: true
    restaurantType?: true
    additionalInfo?: true
  }

  export type RegisteredRestaurantsCountAggregateInputType = {
    restaurantId?: true
    name?: true
    owner?: true
    email?: true
    phone?: true
    restaurantType?: true
    additionalInfo?: true
    _all?: true
  }

  export type RegisteredRestaurantsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RegisteredRestaurants to aggregate.
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RegisteredRestaurants to fetch.
     */
    orderBy?: RegisteredRestaurantsOrderByWithRelationInput | RegisteredRestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RegisteredRestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RegisteredRestaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RegisteredRestaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned RegisteredRestaurants
    **/
    _count?: true | RegisteredRestaurantsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RegisteredRestaurantsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RegisteredRestaurantsMaxAggregateInputType
  }

  export type GetRegisteredRestaurantsAggregateType<T extends RegisteredRestaurantsAggregateArgs> = {
        [P in keyof T & keyof AggregateRegisteredRestaurants]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRegisteredRestaurants[P]>
      : GetScalarType<T[P], AggregateRegisteredRestaurants[P]>
  }




  export type RegisteredRestaurantsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RegisteredRestaurantsWhereInput
    orderBy?: RegisteredRestaurantsOrderByWithAggregationInput | RegisteredRestaurantsOrderByWithAggregationInput[]
    by: RegisteredRestaurantsScalarFieldEnum[] | RegisteredRestaurantsScalarFieldEnum
    having?: RegisteredRestaurantsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RegisteredRestaurantsCountAggregateInputType | true
    _min?: RegisteredRestaurantsMinAggregateInputType
    _max?: RegisteredRestaurantsMaxAggregateInputType
  }

  export type RegisteredRestaurantsGroupByOutputType = {
    restaurantId: string
    name: string
    owner: string
    email: string
    phone: string
    restaurantType: string
    additionalInfo: string | null
    _count: RegisteredRestaurantsCountAggregateOutputType | null
    _min: RegisteredRestaurantsMinAggregateOutputType | null
    _max: RegisteredRestaurantsMaxAggregateOutputType | null
  }

  type GetRegisteredRestaurantsGroupByPayload<T extends RegisteredRestaurantsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RegisteredRestaurantsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RegisteredRestaurantsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RegisteredRestaurantsGroupByOutputType[P]>
            : GetScalarType<T[P], RegisteredRestaurantsGroupByOutputType[P]>
        }
      >
    >


  export type RegisteredRestaurantsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    owner?: boolean
    email?: boolean
    phone?: boolean
    restaurantType?: boolean
    additionalInfo?: boolean
  }, ExtArgs["result"]["registeredRestaurants"]>

  export type RegisteredRestaurantsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    owner?: boolean
    email?: boolean
    phone?: boolean
    restaurantType?: boolean
    additionalInfo?: boolean
  }, ExtArgs["result"]["registeredRestaurants"]>

  export type RegisteredRestaurantsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    owner?: boolean
    email?: boolean
    phone?: boolean
    restaurantType?: boolean
    additionalInfo?: boolean
  }, ExtArgs["result"]["registeredRestaurants"]>

  export type RegisteredRestaurantsSelectScalar = {
    restaurantId?: boolean
    name?: boolean
    owner?: boolean
    email?: boolean
    phone?: boolean
    restaurantType?: boolean
    additionalInfo?: boolean
  }

  export type RegisteredRestaurantsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"restaurantId" | "name" | "owner" | "email" | "phone" | "restaurantType" | "additionalInfo", ExtArgs["result"]["registeredRestaurants"]>

  export type $RegisteredRestaurantsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "RegisteredRestaurants"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      restaurantId: string
      name: string
      owner: string
      email: string
      phone: string
      restaurantType: string
      additionalInfo: string | null
    }, ExtArgs["result"]["registeredRestaurants"]>
    composites: {}
  }

  type RegisteredRestaurantsGetPayload<S extends boolean | null | undefined | RegisteredRestaurantsDefaultArgs> = $Result.GetResult<Prisma.$RegisteredRestaurantsPayload, S>

  type RegisteredRestaurantsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<RegisteredRestaurantsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: RegisteredRestaurantsCountAggregateInputType | true
    }

  export interface RegisteredRestaurantsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['RegisteredRestaurants'], meta: { name: 'RegisteredRestaurants' } }
    /**
     * Find zero or one RegisteredRestaurants that matches the filter.
     * @param {RegisteredRestaurantsFindUniqueArgs} args - Arguments to find a RegisteredRestaurants
     * @example
     * // Get one RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RegisteredRestaurantsFindUniqueArgs>(args: SelectSubset<T, RegisteredRestaurantsFindUniqueArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one RegisteredRestaurants that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {RegisteredRestaurantsFindUniqueOrThrowArgs} args - Arguments to find a RegisteredRestaurants
     * @example
     * // Get one RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RegisteredRestaurantsFindUniqueOrThrowArgs>(args: SelectSubset<T, RegisteredRestaurantsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first RegisteredRestaurants that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsFindFirstArgs} args - Arguments to find a RegisteredRestaurants
     * @example
     * // Get one RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RegisteredRestaurantsFindFirstArgs>(args?: SelectSubset<T, RegisteredRestaurantsFindFirstArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first RegisteredRestaurants that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsFindFirstOrThrowArgs} args - Arguments to find a RegisteredRestaurants
     * @example
     * // Get one RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RegisteredRestaurantsFindFirstOrThrowArgs>(args?: SelectSubset<T, RegisteredRestaurantsFindFirstOrThrowArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more RegisteredRestaurants that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findMany()
     * 
     * // Get first 10 RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.findMany({ take: 10 })
     * 
     * // Only select the `restaurantId`
     * const registeredRestaurantsWithRestaurantIdOnly = await prisma.registeredRestaurants.findMany({ select: { restaurantId: true } })
     * 
     */
    findMany<T extends RegisteredRestaurantsFindManyArgs>(args?: SelectSubset<T, RegisteredRestaurantsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a RegisteredRestaurants.
     * @param {RegisteredRestaurantsCreateArgs} args - Arguments to create a RegisteredRestaurants.
     * @example
     * // Create one RegisteredRestaurants
     * const RegisteredRestaurants = await prisma.registeredRestaurants.create({
     *   data: {
     *     // ... data to create a RegisteredRestaurants
     *   }
     * })
     * 
     */
    create<T extends RegisteredRestaurantsCreateArgs>(args: SelectSubset<T, RegisteredRestaurantsCreateArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many RegisteredRestaurants.
     * @param {RegisteredRestaurantsCreateManyArgs} args - Arguments to create many RegisteredRestaurants.
     * @example
     * // Create many RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RegisteredRestaurantsCreateManyArgs>(args?: SelectSubset<T, RegisteredRestaurantsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many RegisteredRestaurants and returns the data saved in the database.
     * @param {RegisteredRestaurantsCreateManyAndReturnArgs} args - Arguments to create many RegisteredRestaurants.
     * @example
     * // Create many RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many RegisteredRestaurants and only return the `restaurantId`
     * const registeredRestaurantsWithRestaurantIdOnly = await prisma.registeredRestaurants.createManyAndReturn({
     *   select: { restaurantId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RegisteredRestaurantsCreateManyAndReturnArgs>(args?: SelectSubset<T, RegisteredRestaurantsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a RegisteredRestaurants.
     * @param {RegisteredRestaurantsDeleteArgs} args - Arguments to delete one RegisteredRestaurants.
     * @example
     * // Delete one RegisteredRestaurants
     * const RegisteredRestaurants = await prisma.registeredRestaurants.delete({
     *   where: {
     *     // ... filter to delete one RegisteredRestaurants
     *   }
     * })
     * 
     */
    delete<T extends RegisteredRestaurantsDeleteArgs>(args: SelectSubset<T, RegisteredRestaurantsDeleteArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one RegisteredRestaurants.
     * @param {RegisteredRestaurantsUpdateArgs} args - Arguments to update one RegisteredRestaurants.
     * @example
     * // Update one RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RegisteredRestaurantsUpdateArgs>(args: SelectSubset<T, RegisteredRestaurantsUpdateArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more RegisteredRestaurants.
     * @param {RegisteredRestaurantsDeleteManyArgs} args - Arguments to filter RegisteredRestaurants to delete.
     * @example
     * // Delete a few RegisteredRestaurants
     * const { count } = await prisma.registeredRestaurants.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RegisteredRestaurantsDeleteManyArgs>(args?: SelectSubset<T, RegisteredRestaurantsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more RegisteredRestaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RegisteredRestaurantsUpdateManyArgs>(args: SelectSubset<T, RegisteredRestaurantsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more RegisteredRestaurants and returns the data updated in the database.
     * @param {RegisteredRestaurantsUpdateManyAndReturnArgs} args - Arguments to update many RegisteredRestaurants.
     * @example
     * // Update many RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more RegisteredRestaurants and only return the `restaurantId`
     * const registeredRestaurantsWithRestaurantIdOnly = await prisma.registeredRestaurants.updateManyAndReturn({
     *   select: { restaurantId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends RegisteredRestaurantsUpdateManyAndReturnArgs>(args: SelectSubset<T, RegisteredRestaurantsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one RegisteredRestaurants.
     * @param {RegisteredRestaurantsUpsertArgs} args - Arguments to update or create a RegisteredRestaurants.
     * @example
     * // Update or create a RegisteredRestaurants
     * const registeredRestaurants = await prisma.registeredRestaurants.upsert({
     *   create: {
     *     // ... data to create a RegisteredRestaurants
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the RegisteredRestaurants we want to update
     *   }
     * })
     */
    upsert<T extends RegisteredRestaurantsUpsertArgs>(args: SelectSubset<T, RegisteredRestaurantsUpsertArgs<ExtArgs>>): Prisma__RegisteredRestaurantsClient<$Result.GetResult<Prisma.$RegisteredRestaurantsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of RegisteredRestaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsCountArgs} args - Arguments to filter RegisteredRestaurants to count.
     * @example
     * // Count the number of RegisteredRestaurants
     * const count = await prisma.registeredRestaurants.count({
     *   where: {
     *     // ... the filter for the RegisteredRestaurants we want to count
     *   }
     * })
    **/
    count<T extends RegisteredRestaurantsCountArgs>(
      args?: Subset<T, RegisteredRestaurantsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RegisteredRestaurantsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a RegisteredRestaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RegisteredRestaurantsAggregateArgs>(args: Subset<T, RegisteredRestaurantsAggregateArgs>): Prisma.PrismaPromise<GetRegisteredRestaurantsAggregateType<T>>

    /**
     * Group by RegisteredRestaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RegisteredRestaurantsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RegisteredRestaurantsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RegisteredRestaurantsGroupByArgs['orderBy'] }
        : { orderBy?: RegisteredRestaurantsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RegisteredRestaurantsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRegisteredRestaurantsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the RegisteredRestaurants model
   */
  readonly fields: RegisteredRestaurantsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for RegisteredRestaurants.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RegisteredRestaurantsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the RegisteredRestaurants model
   */
  interface RegisteredRestaurantsFieldRefs {
    readonly restaurantId: FieldRef<"RegisteredRestaurants", 'String'>
    readonly name: FieldRef<"RegisteredRestaurants", 'String'>
    readonly owner: FieldRef<"RegisteredRestaurants", 'String'>
    readonly email: FieldRef<"RegisteredRestaurants", 'String'>
    readonly phone: FieldRef<"RegisteredRestaurants", 'String'>
    readonly restaurantType: FieldRef<"RegisteredRestaurants", 'String'>
    readonly additionalInfo: FieldRef<"RegisteredRestaurants", 'String'>
  }
    

  // Custom InputTypes
  /**
   * RegisteredRestaurants findUnique
   */
  export type RegisteredRestaurantsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which RegisteredRestaurants to fetch.
     */
    where: RegisteredRestaurantsWhereUniqueInput
  }

  /**
   * RegisteredRestaurants findUniqueOrThrow
   */
  export type RegisteredRestaurantsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which RegisteredRestaurants to fetch.
     */
    where: RegisteredRestaurantsWhereUniqueInput
  }

  /**
   * RegisteredRestaurants findFirst
   */
  export type RegisteredRestaurantsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which RegisteredRestaurants to fetch.
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RegisteredRestaurants to fetch.
     */
    orderBy?: RegisteredRestaurantsOrderByWithRelationInput | RegisteredRestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RegisteredRestaurants.
     */
    cursor?: RegisteredRestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RegisteredRestaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RegisteredRestaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RegisteredRestaurants.
     */
    distinct?: RegisteredRestaurantsScalarFieldEnum | RegisteredRestaurantsScalarFieldEnum[]
  }

  /**
   * RegisteredRestaurants findFirstOrThrow
   */
  export type RegisteredRestaurantsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which RegisteredRestaurants to fetch.
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RegisteredRestaurants to fetch.
     */
    orderBy?: RegisteredRestaurantsOrderByWithRelationInput | RegisteredRestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for RegisteredRestaurants.
     */
    cursor?: RegisteredRestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RegisteredRestaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RegisteredRestaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of RegisteredRestaurants.
     */
    distinct?: RegisteredRestaurantsScalarFieldEnum | RegisteredRestaurantsScalarFieldEnum[]
  }

  /**
   * RegisteredRestaurants findMany
   */
  export type RegisteredRestaurantsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which RegisteredRestaurants to fetch.
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of RegisteredRestaurants to fetch.
     */
    orderBy?: RegisteredRestaurantsOrderByWithRelationInput | RegisteredRestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing RegisteredRestaurants.
     */
    cursor?: RegisteredRestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` RegisteredRestaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` RegisteredRestaurants.
     */
    skip?: number
    distinct?: RegisteredRestaurantsScalarFieldEnum | RegisteredRestaurantsScalarFieldEnum[]
  }

  /**
   * RegisteredRestaurants create
   */
  export type RegisteredRestaurantsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * The data needed to create a RegisteredRestaurants.
     */
    data: XOR<RegisteredRestaurantsCreateInput, RegisteredRestaurantsUncheckedCreateInput>
  }

  /**
   * RegisteredRestaurants createMany
   */
  export type RegisteredRestaurantsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many RegisteredRestaurants.
     */
    data: RegisteredRestaurantsCreateManyInput | RegisteredRestaurantsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * RegisteredRestaurants createManyAndReturn
   */
  export type RegisteredRestaurantsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * The data used to create many RegisteredRestaurants.
     */
    data: RegisteredRestaurantsCreateManyInput | RegisteredRestaurantsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * RegisteredRestaurants update
   */
  export type RegisteredRestaurantsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * The data needed to update a RegisteredRestaurants.
     */
    data: XOR<RegisteredRestaurantsUpdateInput, RegisteredRestaurantsUncheckedUpdateInput>
    /**
     * Choose, which RegisteredRestaurants to update.
     */
    where: RegisteredRestaurantsWhereUniqueInput
  }

  /**
   * RegisteredRestaurants updateMany
   */
  export type RegisteredRestaurantsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update RegisteredRestaurants.
     */
    data: XOR<RegisteredRestaurantsUpdateManyMutationInput, RegisteredRestaurantsUncheckedUpdateManyInput>
    /**
     * Filter which RegisteredRestaurants to update
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * Limit how many RegisteredRestaurants to update.
     */
    limit?: number
  }

  /**
   * RegisteredRestaurants updateManyAndReturn
   */
  export type RegisteredRestaurantsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * The data used to update RegisteredRestaurants.
     */
    data: XOR<RegisteredRestaurantsUpdateManyMutationInput, RegisteredRestaurantsUncheckedUpdateManyInput>
    /**
     * Filter which RegisteredRestaurants to update
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * Limit how many RegisteredRestaurants to update.
     */
    limit?: number
  }

  /**
   * RegisteredRestaurants upsert
   */
  export type RegisteredRestaurantsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * The filter to search for the RegisteredRestaurants to update in case it exists.
     */
    where: RegisteredRestaurantsWhereUniqueInput
    /**
     * In case the RegisteredRestaurants found by the `where` argument doesn't exist, create a new RegisteredRestaurants with this data.
     */
    create: XOR<RegisteredRestaurantsCreateInput, RegisteredRestaurantsUncheckedCreateInput>
    /**
     * In case the RegisteredRestaurants was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RegisteredRestaurantsUpdateInput, RegisteredRestaurantsUncheckedUpdateInput>
  }

  /**
   * RegisteredRestaurants delete
   */
  export type RegisteredRestaurantsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
    /**
     * Filter which RegisteredRestaurants to delete.
     */
    where: RegisteredRestaurantsWhereUniqueInput
  }

  /**
   * RegisteredRestaurants deleteMany
   */
  export type RegisteredRestaurantsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which RegisteredRestaurants to delete
     */
    where?: RegisteredRestaurantsWhereInput
    /**
     * Limit how many RegisteredRestaurants to delete.
     */
    limit?: number
  }

  /**
   * RegisteredRestaurants without action
   */
  export type RegisteredRestaurantsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the RegisteredRestaurants
     */
    select?: RegisteredRestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the RegisteredRestaurants
     */
    omit?: RegisteredRestaurantsOmit<ExtArgs> | null
  }


  /**
   * Model Restaurants
   */

  export type AggregateRestaurants = {
    _count: RestaurantsCountAggregateOutputType | null
    _min: RestaurantsMinAggregateOutputType | null
    _max: RestaurantsMaxAggregateOutputType | null
  }

  export type RestaurantsMinAggregateOutputType = {
    restaurantId: string | null
    name: string | null
    email: string | null
  }

  export type RestaurantsMaxAggregateOutputType = {
    restaurantId: string | null
    name: string | null
    email: string | null
  }

  export type RestaurantsCountAggregateOutputType = {
    restaurantId: number
    name: number
    email: number
    _all: number
  }


  export type RestaurantsMinAggregateInputType = {
    restaurantId?: true
    name?: true
    email?: true
  }

  export type RestaurantsMaxAggregateInputType = {
    restaurantId?: true
    name?: true
    email?: true
  }

  export type RestaurantsCountAggregateInputType = {
    restaurantId?: true
    name?: true
    email?: true
    _all?: true
  }

  export type RestaurantsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Restaurants to aggregate.
     */
    where?: RestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Restaurants to fetch.
     */
    orderBy?: RestaurantsOrderByWithRelationInput | RestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: RestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Restaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Restaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Restaurants
    **/
    _count?: true | RestaurantsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: RestaurantsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: RestaurantsMaxAggregateInputType
  }

  export type GetRestaurantsAggregateType<T extends RestaurantsAggregateArgs> = {
        [P in keyof T & keyof AggregateRestaurants]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateRestaurants[P]>
      : GetScalarType<T[P], AggregateRestaurants[P]>
  }




  export type RestaurantsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: RestaurantsWhereInput
    orderBy?: RestaurantsOrderByWithAggregationInput | RestaurantsOrderByWithAggregationInput[]
    by: RestaurantsScalarFieldEnum[] | RestaurantsScalarFieldEnum
    having?: RestaurantsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: RestaurantsCountAggregateInputType | true
    _min?: RestaurantsMinAggregateInputType
    _max?: RestaurantsMaxAggregateInputType
  }

  export type RestaurantsGroupByOutputType = {
    restaurantId: string
    name: string
    email: string
    _count: RestaurantsCountAggregateOutputType | null
    _min: RestaurantsMinAggregateOutputType | null
    _max: RestaurantsMaxAggregateOutputType | null
  }

  type GetRestaurantsGroupByPayload<T extends RestaurantsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<RestaurantsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof RestaurantsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], RestaurantsGroupByOutputType[P]>
            : GetScalarType<T[P], RestaurantsGroupByOutputType[P]>
        }
      >
    >


  export type RestaurantsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    email?: boolean
  }, ExtArgs["result"]["restaurants"]>

  export type RestaurantsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    email?: boolean
  }, ExtArgs["result"]["restaurants"]>

  export type RestaurantsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    restaurantId?: boolean
    name?: boolean
    email?: boolean
  }, ExtArgs["result"]["restaurants"]>

  export type RestaurantsSelectScalar = {
    restaurantId?: boolean
    name?: boolean
    email?: boolean
  }

  export type RestaurantsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"restaurantId" | "name" | "email", ExtArgs["result"]["restaurants"]>

  export type $RestaurantsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Restaurants"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      restaurantId: string
      name: string
      email: string
    }, ExtArgs["result"]["restaurants"]>
    composites: {}
  }

  type RestaurantsGetPayload<S extends boolean | null | undefined | RestaurantsDefaultArgs> = $Result.GetResult<Prisma.$RestaurantsPayload, S>

  type RestaurantsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<RestaurantsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: RestaurantsCountAggregateInputType | true
    }

  export interface RestaurantsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Restaurants'], meta: { name: 'Restaurants' } }
    /**
     * Find zero or one Restaurants that matches the filter.
     * @param {RestaurantsFindUniqueArgs} args - Arguments to find a Restaurants
     * @example
     * // Get one Restaurants
     * const restaurants = await prisma.restaurants.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends RestaurantsFindUniqueArgs>(args: SelectSubset<T, RestaurantsFindUniqueArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Restaurants that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {RestaurantsFindUniqueOrThrowArgs} args - Arguments to find a Restaurants
     * @example
     * // Get one Restaurants
     * const restaurants = await prisma.restaurants.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends RestaurantsFindUniqueOrThrowArgs>(args: SelectSubset<T, RestaurantsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Restaurants that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsFindFirstArgs} args - Arguments to find a Restaurants
     * @example
     * // Get one Restaurants
     * const restaurants = await prisma.restaurants.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends RestaurantsFindFirstArgs>(args?: SelectSubset<T, RestaurantsFindFirstArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Restaurants that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsFindFirstOrThrowArgs} args - Arguments to find a Restaurants
     * @example
     * // Get one Restaurants
     * const restaurants = await prisma.restaurants.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends RestaurantsFindFirstOrThrowArgs>(args?: SelectSubset<T, RestaurantsFindFirstOrThrowArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Restaurants that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Restaurants
     * const restaurants = await prisma.restaurants.findMany()
     * 
     * // Get first 10 Restaurants
     * const restaurants = await prisma.restaurants.findMany({ take: 10 })
     * 
     * // Only select the `restaurantId`
     * const restaurantsWithRestaurantIdOnly = await prisma.restaurants.findMany({ select: { restaurantId: true } })
     * 
     */
    findMany<T extends RestaurantsFindManyArgs>(args?: SelectSubset<T, RestaurantsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Restaurants.
     * @param {RestaurantsCreateArgs} args - Arguments to create a Restaurants.
     * @example
     * // Create one Restaurants
     * const Restaurants = await prisma.restaurants.create({
     *   data: {
     *     // ... data to create a Restaurants
     *   }
     * })
     * 
     */
    create<T extends RestaurantsCreateArgs>(args: SelectSubset<T, RestaurantsCreateArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Restaurants.
     * @param {RestaurantsCreateManyArgs} args - Arguments to create many Restaurants.
     * @example
     * // Create many Restaurants
     * const restaurants = await prisma.restaurants.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends RestaurantsCreateManyArgs>(args?: SelectSubset<T, RestaurantsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Restaurants and returns the data saved in the database.
     * @param {RestaurantsCreateManyAndReturnArgs} args - Arguments to create many Restaurants.
     * @example
     * // Create many Restaurants
     * const restaurants = await prisma.restaurants.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Restaurants and only return the `restaurantId`
     * const restaurantsWithRestaurantIdOnly = await prisma.restaurants.createManyAndReturn({
     *   select: { restaurantId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends RestaurantsCreateManyAndReturnArgs>(args?: SelectSubset<T, RestaurantsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Restaurants.
     * @param {RestaurantsDeleteArgs} args - Arguments to delete one Restaurants.
     * @example
     * // Delete one Restaurants
     * const Restaurants = await prisma.restaurants.delete({
     *   where: {
     *     // ... filter to delete one Restaurants
     *   }
     * })
     * 
     */
    delete<T extends RestaurantsDeleteArgs>(args: SelectSubset<T, RestaurantsDeleteArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Restaurants.
     * @param {RestaurantsUpdateArgs} args - Arguments to update one Restaurants.
     * @example
     * // Update one Restaurants
     * const restaurants = await prisma.restaurants.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends RestaurantsUpdateArgs>(args: SelectSubset<T, RestaurantsUpdateArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Restaurants.
     * @param {RestaurantsDeleteManyArgs} args - Arguments to filter Restaurants to delete.
     * @example
     * // Delete a few Restaurants
     * const { count } = await prisma.restaurants.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends RestaurantsDeleteManyArgs>(args?: SelectSubset<T, RestaurantsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Restaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Restaurants
     * const restaurants = await prisma.restaurants.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends RestaurantsUpdateManyArgs>(args: SelectSubset<T, RestaurantsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Restaurants and returns the data updated in the database.
     * @param {RestaurantsUpdateManyAndReturnArgs} args - Arguments to update many Restaurants.
     * @example
     * // Update many Restaurants
     * const restaurants = await prisma.restaurants.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Restaurants and only return the `restaurantId`
     * const restaurantsWithRestaurantIdOnly = await prisma.restaurants.updateManyAndReturn({
     *   select: { restaurantId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends RestaurantsUpdateManyAndReturnArgs>(args: SelectSubset<T, RestaurantsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Restaurants.
     * @param {RestaurantsUpsertArgs} args - Arguments to update or create a Restaurants.
     * @example
     * // Update or create a Restaurants
     * const restaurants = await prisma.restaurants.upsert({
     *   create: {
     *     // ... data to create a Restaurants
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Restaurants we want to update
     *   }
     * })
     */
    upsert<T extends RestaurantsUpsertArgs>(args: SelectSubset<T, RestaurantsUpsertArgs<ExtArgs>>): Prisma__RestaurantsClient<$Result.GetResult<Prisma.$RestaurantsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Restaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsCountArgs} args - Arguments to filter Restaurants to count.
     * @example
     * // Count the number of Restaurants
     * const count = await prisma.restaurants.count({
     *   where: {
     *     // ... the filter for the Restaurants we want to count
     *   }
     * })
    **/
    count<T extends RestaurantsCountArgs>(
      args?: Subset<T, RestaurantsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], RestaurantsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Restaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends RestaurantsAggregateArgs>(args: Subset<T, RestaurantsAggregateArgs>): Prisma.PrismaPromise<GetRestaurantsAggregateType<T>>

    /**
     * Group by Restaurants.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {RestaurantsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends RestaurantsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: RestaurantsGroupByArgs['orderBy'] }
        : { orderBy?: RestaurantsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, RestaurantsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetRestaurantsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Restaurants model
   */
  readonly fields: RestaurantsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Restaurants.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__RestaurantsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Restaurants model
   */
  interface RestaurantsFieldRefs {
    readonly restaurantId: FieldRef<"Restaurants", 'String'>
    readonly name: FieldRef<"Restaurants", 'String'>
    readonly email: FieldRef<"Restaurants", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Restaurants findUnique
   */
  export type RestaurantsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which Restaurants to fetch.
     */
    where: RestaurantsWhereUniqueInput
  }

  /**
   * Restaurants findUniqueOrThrow
   */
  export type RestaurantsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which Restaurants to fetch.
     */
    where: RestaurantsWhereUniqueInput
  }

  /**
   * Restaurants findFirst
   */
  export type RestaurantsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which Restaurants to fetch.
     */
    where?: RestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Restaurants to fetch.
     */
    orderBy?: RestaurantsOrderByWithRelationInput | RestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Restaurants.
     */
    cursor?: RestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Restaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Restaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Restaurants.
     */
    distinct?: RestaurantsScalarFieldEnum | RestaurantsScalarFieldEnum[]
  }

  /**
   * Restaurants findFirstOrThrow
   */
  export type RestaurantsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which Restaurants to fetch.
     */
    where?: RestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Restaurants to fetch.
     */
    orderBy?: RestaurantsOrderByWithRelationInput | RestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Restaurants.
     */
    cursor?: RestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Restaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Restaurants.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Restaurants.
     */
    distinct?: RestaurantsScalarFieldEnum | RestaurantsScalarFieldEnum[]
  }

  /**
   * Restaurants findMany
   */
  export type RestaurantsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter, which Restaurants to fetch.
     */
    where?: RestaurantsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Restaurants to fetch.
     */
    orderBy?: RestaurantsOrderByWithRelationInput | RestaurantsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Restaurants.
     */
    cursor?: RestaurantsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Restaurants from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Restaurants.
     */
    skip?: number
    distinct?: RestaurantsScalarFieldEnum | RestaurantsScalarFieldEnum[]
  }

  /**
   * Restaurants create
   */
  export type RestaurantsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * The data needed to create a Restaurants.
     */
    data: XOR<RestaurantsCreateInput, RestaurantsUncheckedCreateInput>
  }

  /**
   * Restaurants createMany
   */
  export type RestaurantsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Restaurants.
     */
    data: RestaurantsCreateManyInput | RestaurantsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Restaurants createManyAndReturn
   */
  export type RestaurantsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * The data used to create many Restaurants.
     */
    data: RestaurantsCreateManyInput | RestaurantsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Restaurants update
   */
  export type RestaurantsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * The data needed to update a Restaurants.
     */
    data: XOR<RestaurantsUpdateInput, RestaurantsUncheckedUpdateInput>
    /**
     * Choose, which Restaurants to update.
     */
    where: RestaurantsWhereUniqueInput
  }

  /**
   * Restaurants updateMany
   */
  export type RestaurantsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Restaurants.
     */
    data: XOR<RestaurantsUpdateManyMutationInput, RestaurantsUncheckedUpdateManyInput>
    /**
     * Filter which Restaurants to update
     */
    where?: RestaurantsWhereInput
    /**
     * Limit how many Restaurants to update.
     */
    limit?: number
  }

  /**
   * Restaurants updateManyAndReturn
   */
  export type RestaurantsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * The data used to update Restaurants.
     */
    data: XOR<RestaurantsUpdateManyMutationInput, RestaurantsUncheckedUpdateManyInput>
    /**
     * Filter which Restaurants to update
     */
    where?: RestaurantsWhereInput
    /**
     * Limit how many Restaurants to update.
     */
    limit?: number
  }

  /**
   * Restaurants upsert
   */
  export type RestaurantsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * The filter to search for the Restaurants to update in case it exists.
     */
    where: RestaurantsWhereUniqueInput
    /**
     * In case the Restaurants found by the `where` argument doesn't exist, create a new Restaurants with this data.
     */
    create: XOR<RestaurantsCreateInput, RestaurantsUncheckedCreateInput>
    /**
     * In case the Restaurants was found with the provided `where` argument, update it with this data.
     */
    update: XOR<RestaurantsUpdateInput, RestaurantsUncheckedUpdateInput>
  }

  /**
   * Restaurants delete
   */
  export type RestaurantsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
    /**
     * Filter which Restaurants to delete.
     */
    where: RestaurantsWhereUniqueInput
  }

  /**
   * Restaurants deleteMany
   */
  export type RestaurantsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Restaurants to delete
     */
    where?: RestaurantsWhereInput
    /**
     * Limit how many Restaurants to delete.
     */
    limit?: number
  }

  /**
   * Restaurants without action
   */
  export type RestaurantsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Restaurants
     */
    select?: RestaurantsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Restaurants
     */
    omit?: RestaurantsOmit<ExtArgs> | null
  }


  /**
   * Model Users
   */

  export type AggregateUsers = {
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  export type UsersMinAggregateOutputType = {
    userId: string | null
    firstName: string | null
    lastName: string | null
    email: string | null
    favouriteRestaurant: string | null
  }

  export type UsersMaxAggregateOutputType = {
    userId: string | null
    firstName: string | null
    lastName: string | null
    email: string | null
    favouriteRestaurant: string | null
  }

  export type UsersCountAggregateOutputType = {
    userId: number
    firstName: number
    lastName: number
    email: number
    favouriteRestaurant: number
    _all: number
  }


  export type UsersMinAggregateInputType = {
    userId?: true
    firstName?: true
    lastName?: true
    email?: true
    favouriteRestaurant?: true
  }

  export type UsersMaxAggregateInputType = {
    userId?: true
    firstName?: true
    lastName?: true
    email?: true
    favouriteRestaurant?: true
  }

  export type UsersCountAggregateInputType = {
    userId?: true
    firstName?: true
    lastName?: true
    email?: true
    favouriteRestaurant?: true
    _all?: true
  }

  export type UsersAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to aggregate.
     */
    where?: UsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UsersOrderByWithRelationInput | UsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UsersCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UsersMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UsersMaxAggregateInputType
  }

  export type GetUsersAggregateType<T extends UsersAggregateArgs> = {
        [P in keyof T & keyof AggregateUsers]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUsers[P]>
      : GetScalarType<T[P], AggregateUsers[P]>
  }




  export type UsersGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UsersWhereInput
    orderBy?: UsersOrderByWithAggregationInput | UsersOrderByWithAggregationInput[]
    by: UsersScalarFieldEnum[] | UsersScalarFieldEnum
    having?: UsersScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UsersCountAggregateInputType | true
    _min?: UsersMinAggregateInputType
    _max?: UsersMaxAggregateInputType
  }

  export type UsersGroupByOutputType = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
    _count: UsersCountAggregateOutputType | null
    _min: UsersMinAggregateOutputType | null
    _max: UsersMaxAggregateOutputType | null
  }

  type GetUsersGroupByPayload<T extends UsersGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UsersGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UsersGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UsersGroupByOutputType[P]>
            : GetScalarType<T[P], UsersGroupByOutputType[P]>
        }
      >
    >


  export type UsersSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    favouriteRestaurant?: boolean
    EarlyAccessUsers?: boolean | Users$EarlyAccessUsersArgs<ExtArgs>
  }, ExtArgs["result"]["users"]>

  export type UsersSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    favouriteRestaurant?: boolean
  }, ExtArgs["result"]["users"]>

  export type UsersSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    userId?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    favouriteRestaurant?: boolean
  }, ExtArgs["result"]["users"]>

  export type UsersSelectScalar = {
    userId?: boolean
    firstName?: boolean
    lastName?: boolean
    email?: boolean
    favouriteRestaurant?: boolean
  }

  export type UsersOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"userId" | "firstName" | "lastName" | "email" | "favouriteRestaurant", ExtArgs["result"]["users"]>
  export type UsersInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    EarlyAccessUsers?: boolean | Users$EarlyAccessUsersArgs<ExtArgs>
  }
  export type UsersIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UsersIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UsersPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Users"
    objects: {
      EarlyAccessUsers: Prisma.$EarlyAccessUsersPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      userId: string
      firstName: string
      lastName: string
      email: string
      favouriteRestaurant: string
    }, ExtArgs["result"]["users"]>
    composites: {}
  }

  type UsersGetPayload<S extends boolean | null | undefined | UsersDefaultArgs> = $Result.GetResult<Prisma.$UsersPayload, S>

  type UsersCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UsersFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UsersCountAggregateInputType | true
    }

  export interface UsersDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Users'], meta: { name: 'Users' } }
    /**
     * Find zero or one Users that matches the filter.
     * @param {UsersFindUniqueArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UsersFindUniqueArgs>(args: SelectSubset<T, UsersFindUniqueArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Users that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UsersFindUniqueOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UsersFindUniqueOrThrowArgs>(args: SelectSubset<T, UsersFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersFindFirstArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UsersFindFirstArgs>(args?: SelectSubset<T, UsersFindFirstArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Users that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersFindFirstOrThrowArgs} args - Arguments to find a Users
     * @example
     * // Get one Users
     * const users = await prisma.users.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UsersFindFirstOrThrowArgs>(args?: SelectSubset<T, UsersFindFirstOrThrowArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.users.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.users.findMany({ take: 10 })
     * 
     * // Only select the `userId`
     * const usersWithUserIdOnly = await prisma.users.findMany({ select: { userId: true } })
     * 
     */
    findMany<T extends UsersFindManyArgs>(args?: SelectSubset<T, UsersFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Users.
     * @param {UsersCreateArgs} args - Arguments to create a Users.
     * @example
     * // Create one Users
     * const Users = await prisma.users.create({
     *   data: {
     *     // ... data to create a Users
     *   }
     * })
     * 
     */
    create<T extends UsersCreateArgs>(args: SelectSubset<T, UsersCreateArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UsersCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const users = await prisma.users.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UsersCreateManyArgs>(args?: SelectSubset<T, UsersCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UsersCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const users = await prisma.users.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `userId`
     * const usersWithUserIdOnly = await prisma.users.createManyAndReturn({
     *   select: { userId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UsersCreateManyAndReturnArgs>(args?: SelectSubset<T, UsersCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Users.
     * @param {UsersDeleteArgs} args - Arguments to delete one Users.
     * @example
     * // Delete one Users
     * const Users = await prisma.users.delete({
     *   where: {
     *     // ... filter to delete one Users
     *   }
     * })
     * 
     */
    delete<T extends UsersDeleteArgs>(args: SelectSubset<T, UsersDeleteArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Users.
     * @param {UsersUpdateArgs} args - Arguments to update one Users.
     * @example
     * // Update one Users
     * const users = await prisma.users.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UsersUpdateArgs>(args: SelectSubset<T, UsersUpdateArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UsersDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.users.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UsersDeleteManyArgs>(args?: SelectSubset<T, UsersDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const users = await prisma.users.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UsersUpdateManyArgs>(args: SelectSubset<T, UsersUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UsersUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const users = await prisma.users.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `userId`
     * const usersWithUserIdOnly = await prisma.users.updateManyAndReturn({
     *   select: { userId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UsersUpdateManyAndReturnArgs>(args: SelectSubset<T, UsersUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Users.
     * @param {UsersUpsertArgs} args - Arguments to update or create a Users.
     * @example
     * // Update or create a Users
     * const users = await prisma.users.upsert({
     *   create: {
     *     // ... data to create a Users
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Users we want to update
     *   }
     * })
     */
    upsert<T extends UsersUpsertArgs>(args: SelectSubset<T, UsersUpsertArgs<ExtArgs>>): Prisma__UsersClient<$Result.GetResult<Prisma.$UsersPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.users.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UsersCountArgs>(
      args?: Subset<T, UsersCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UsersCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UsersAggregateArgs>(args: Subset<T, UsersAggregateArgs>): Prisma.PrismaPromise<GetUsersAggregateType<T>>

    /**
     * Group by Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UsersGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UsersGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UsersGroupByArgs['orderBy'] }
        : { orderBy?: UsersGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UsersGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUsersGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Users model
   */
  readonly fields: UsersFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Users.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UsersClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    EarlyAccessUsers<T extends Users$EarlyAccessUsersArgs<ExtArgs> = {}>(args?: Subset<T, Users$EarlyAccessUsersArgs<ExtArgs>>): Prisma__EarlyAccessUsersClient<$Result.GetResult<Prisma.$EarlyAccessUsersPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Users model
   */
  interface UsersFieldRefs {
    readonly userId: FieldRef<"Users", 'String'>
    readonly firstName: FieldRef<"Users", 'String'>
    readonly lastName: FieldRef<"Users", 'String'>
    readonly email: FieldRef<"Users", 'String'>
    readonly favouriteRestaurant: FieldRef<"Users", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Users findUnique
   */
  export type UsersFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where: UsersWhereUniqueInput
  }

  /**
   * Users findUniqueOrThrow
   */
  export type UsersFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where: UsersWhereUniqueInput
  }

  /**
   * Users findFirst
   */
  export type UsersFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UsersOrderByWithRelationInput | UsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * Users findFirstOrThrow
   */
  export type UsersFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UsersOrderByWithRelationInput | UsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * Users findMany
   */
  export type UsersFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UsersWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UsersOrderByWithRelationInput | UsersOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UsersWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UsersScalarFieldEnum | UsersScalarFieldEnum[]
  }

  /**
   * Users create
   */
  export type UsersCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * The data needed to create a Users.
     */
    data: XOR<UsersCreateInput, UsersUncheckedCreateInput>
  }

  /**
   * Users createMany
   */
  export type UsersCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UsersCreateManyInput | UsersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Users createManyAndReturn
   */
  export type UsersCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UsersCreateManyInput | UsersCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Users update
   */
  export type UsersUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * The data needed to update a Users.
     */
    data: XOR<UsersUpdateInput, UsersUncheckedUpdateInput>
    /**
     * Choose, which Users to update.
     */
    where: UsersWhereUniqueInput
  }

  /**
   * Users updateMany
   */
  export type UsersUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UsersUpdateManyMutationInput, UsersUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UsersWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * Users updateManyAndReturn
   */
  export type UsersUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UsersUpdateManyMutationInput, UsersUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UsersWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * Users upsert
   */
  export type UsersUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * The filter to search for the Users to update in case it exists.
     */
    where: UsersWhereUniqueInput
    /**
     * In case the Users found by the `where` argument doesn't exist, create a new Users with this data.
     */
    create: XOR<UsersCreateInput, UsersUncheckedCreateInput>
    /**
     * In case the Users was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UsersUpdateInput, UsersUncheckedUpdateInput>
  }

  /**
   * Users delete
   */
  export type UsersDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
    /**
     * Filter which Users to delete.
     */
    where: UsersWhereUniqueInput
  }

  /**
   * Users deleteMany
   */
  export type UsersDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UsersWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * Users.EarlyAccessUsers
   */
  export type Users$EarlyAccessUsersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the EarlyAccessUsers
     */
    select?: EarlyAccessUsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the EarlyAccessUsers
     */
    omit?: EarlyAccessUsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: EarlyAccessUsersInclude<ExtArgs> | null
    where?: EarlyAccessUsersWhereInput
  }

  /**
   * Users without action
   */
  export type UsersDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Users
     */
    select?: UsersSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Users
     */
    omit?: UsersOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UsersInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const DevNotificationScalarFieldEnum: {
    id: 'id',
    endpoint: 'endpoint',
    merchantId: 'merchantId',
    merchantName: 'merchantName',
    amount: 'amount',
    mobileNumber: 'mobileNumber',
    clientReference: 'clientReference',
    paymentType: 'paymentType',
    timestamp: 'timestamp'
  };

  export type DevNotificationScalarFieldEnum = (typeof DevNotificationScalarFieldEnum)[keyof typeof DevNotificationScalarFieldEnum]


  export const EarlyAccessUsersScalarFieldEnum: {
    userId: 'userId',
    phoneNumber: 'phoneNumber',
    socialMediaLinks: 'socialMediaLinks',
    approved: 'approved'
  };

  export type EarlyAccessUsersScalarFieldEnum = (typeof EarlyAccessUsersScalarFieldEnum)[keyof typeof EarlyAccessUsersScalarFieldEnum]


  export const RegisteredRestaurantsScalarFieldEnum: {
    restaurantId: 'restaurantId',
    name: 'name',
    owner: 'owner',
    email: 'email',
    phone: 'phone',
    restaurantType: 'restaurantType',
    additionalInfo: 'additionalInfo'
  };

  export type RegisteredRestaurantsScalarFieldEnum = (typeof RegisteredRestaurantsScalarFieldEnum)[keyof typeof RegisteredRestaurantsScalarFieldEnum]


  export const RestaurantsScalarFieldEnum: {
    restaurantId: 'restaurantId',
    name: 'name',
    email: 'email'
  };

  export type RestaurantsScalarFieldEnum = (typeof RestaurantsScalarFieldEnum)[keyof typeof RestaurantsScalarFieldEnum]


  export const UsersScalarFieldEnum: {
    userId: 'userId',
    firstName: 'firstName',
    lastName: 'lastName',
    email: 'email',
    favouriteRestaurant: 'favouriteRestaurant'
  };

  export type UsersScalarFieldEnum = (typeof UsersScalarFieldEnum)[keyof typeof UsersScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Decimal'
   */
  export type DecimalFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Decimal'>
    


  /**
   * Reference to a field of type 'Decimal[]'
   */
  export type ListDecimalFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Decimal[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type DevNotificationWhereInput = {
    AND?: DevNotificationWhereInput | DevNotificationWhereInput[]
    OR?: DevNotificationWhereInput[]
    NOT?: DevNotificationWhereInput | DevNotificationWhereInput[]
    id?: StringFilter<"DevNotification"> | string
    endpoint?: StringFilter<"DevNotification"> | string
    merchantId?: StringFilter<"DevNotification"> | string
    merchantName?: StringFilter<"DevNotification"> | string
    amount?: DecimalFilter<"DevNotification"> | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFilter<"DevNotification"> | string
    clientReference?: StringFilter<"DevNotification"> | string
    paymentType?: StringFilter<"DevNotification"> | string
    timestamp?: DateTimeFilter<"DevNotification"> | Date | string
  }

  export type DevNotificationOrderByWithRelationInput = {
    id?: SortOrder
    endpoint?: SortOrder
    merchantId?: SortOrder
    merchantName?: SortOrder
    amount?: SortOrder
    mobileNumber?: SortOrder
    clientReference?: SortOrder
    paymentType?: SortOrder
    timestamp?: SortOrder
  }

  export type DevNotificationWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: DevNotificationWhereInput | DevNotificationWhereInput[]
    OR?: DevNotificationWhereInput[]
    NOT?: DevNotificationWhereInput | DevNotificationWhereInput[]
    endpoint?: StringFilter<"DevNotification"> | string
    merchantId?: StringFilter<"DevNotification"> | string
    merchantName?: StringFilter<"DevNotification"> | string
    amount?: DecimalFilter<"DevNotification"> | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFilter<"DevNotification"> | string
    clientReference?: StringFilter<"DevNotification"> | string
    paymentType?: StringFilter<"DevNotification"> | string
    timestamp?: DateTimeFilter<"DevNotification"> | Date | string
  }, "id">

  export type DevNotificationOrderByWithAggregationInput = {
    id?: SortOrder
    endpoint?: SortOrder
    merchantId?: SortOrder
    merchantName?: SortOrder
    amount?: SortOrder
    mobileNumber?: SortOrder
    clientReference?: SortOrder
    paymentType?: SortOrder
    timestamp?: SortOrder
    _count?: DevNotificationCountOrderByAggregateInput
    _avg?: DevNotificationAvgOrderByAggregateInput
    _max?: DevNotificationMaxOrderByAggregateInput
    _min?: DevNotificationMinOrderByAggregateInput
    _sum?: DevNotificationSumOrderByAggregateInput
  }

  export type DevNotificationScalarWhereWithAggregatesInput = {
    AND?: DevNotificationScalarWhereWithAggregatesInput | DevNotificationScalarWhereWithAggregatesInput[]
    OR?: DevNotificationScalarWhereWithAggregatesInput[]
    NOT?: DevNotificationScalarWhereWithAggregatesInput | DevNotificationScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"DevNotification"> | string
    endpoint?: StringWithAggregatesFilter<"DevNotification"> | string
    merchantId?: StringWithAggregatesFilter<"DevNotification"> | string
    merchantName?: StringWithAggregatesFilter<"DevNotification"> | string
    amount?: DecimalWithAggregatesFilter<"DevNotification"> | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringWithAggregatesFilter<"DevNotification"> | string
    clientReference?: StringWithAggregatesFilter<"DevNotification"> | string
    paymentType?: StringWithAggregatesFilter<"DevNotification"> | string
    timestamp?: DateTimeWithAggregatesFilter<"DevNotification"> | Date | string
  }

  export type EarlyAccessUsersWhereInput = {
    AND?: EarlyAccessUsersWhereInput | EarlyAccessUsersWhereInput[]
    OR?: EarlyAccessUsersWhereInput[]
    NOT?: EarlyAccessUsersWhereInput | EarlyAccessUsersWhereInput[]
    userId?: StringFilter<"EarlyAccessUsers"> | string
    phoneNumber?: StringFilter<"EarlyAccessUsers"> | string
    socialMediaLinks?: StringNullableListFilter<"EarlyAccessUsers">
    approved?: BoolFilter<"EarlyAccessUsers"> | boolean
    Users?: XOR<UsersScalarRelationFilter, UsersWhereInput>
  }

  export type EarlyAccessUsersOrderByWithRelationInput = {
    userId?: SortOrder
    phoneNumber?: SortOrder
    socialMediaLinks?: SortOrder
    approved?: SortOrder
    Users?: UsersOrderByWithRelationInput
  }

  export type EarlyAccessUsersWhereUniqueInput = Prisma.AtLeast<{
    userId?: string
    AND?: EarlyAccessUsersWhereInput | EarlyAccessUsersWhereInput[]
    OR?: EarlyAccessUsersWhereInput[]
    NOT?: EarlyAccessUsersWhereInput | EarlyAccessUsersWhereInput[]
    phoneNumber?: StringFilter<"EarlyAccessUsers"> | string
    socialMediaLinks?: StringNullableListFilter<"EarlyAccessUsers">
    approved?: BoolFilter<"EarlyAccessUsers"> | boolean
    Users?: XOR<UsersScalarRelationFilter, UsersWhereInput>
  }, "userId" | "userId">

  export type EarlyAccessUsersOrderByWithAggregationInput = {
    userId?: SortOrder
    phoneNumber?: SortOrder
    socialMediaLinks?: SortOrder
    approved?: SortOrder
    _count?: EarlyAccessUsersCountOrderByAggregateInput
    _max?: EarlyAccessUsersMaxOrderByAggregateInput
    _min?: EarlyAccessUsersMinOrderByAggregateInput
  }

  export type EarlyAccessUsersScalarWhereWithAggregatesInput = {
    AND?: EarlyAccessUsersScalarWhereWithAggregatesInput | EarlyAccessUsersScalarWhereWithAggregatesInput[]
    OR?: EarlyAccessUsersScalarWhereWithAggregatesInput[]
    NOT?: EarlyAccessUsersScalarWhereWithAggregatesInput | EarlyAccessUsersScalarWhereWithAggregatesInput[]
    userId?: StringWithAggregatesFilter<"EarlyAccessUsers"> | string
    phoneNumber?: StringWithAggregatesFilter<"EarlyAccessUsers"> | string
    socialMediaLinks?: StringNullableListFilter<"EarlyAccessUsers">
    approved?: BoolWithAggregatesFilter<"EarlyAccessUsers"> | boolean
  }

  export type RegisteredRestaurantsWhereInput = {
    AND?: RegisteredRestaurantsWhereInput | RegisteredRestaurantsWhereInput[]
    OR?: RegisteredRestaurantsWhereInput[]
    NOT?: RegisteredRestaurantsWhereInput | RegisteredRestaurantsWhereInput[]
    restaurantId?: StringFilter<"RegisteredRestaurants"> | string
    name?: StringFilter<"RegisteredRestaurants"> | string
    owner?: StringFilter<"RegisteredRestaurants"> | string
    email?: StringFilter<"RegisteredRestaurants"> | string
    phone?: StringFilter<"RegisteredRestaurants"> | string
    restaurantType?: StringFilter<"RegisteredRestaurants"> | string
    additionalInfo?: StringNullableFilter<"RegisteredRestaurants"> | string | null
  }

  export type RegisteredRestaurantsOrderByWithRelationInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    owner?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    restaurantType?: SortOrder
    additionalInfo?: SortOrderInput | SortOrder
  }

  export type RegisteredRestaurantsWhereUniqueInput = Prisma.AtLeast<{
    restaurantId?: string
    email?: string
    AND?: RegisteredRestaurantsWhereInput | RegisteredRestaurantsWhereInput[]
    OR?: RegisteredRestaurantsWhereInput[]
    NOT?: RegisteredRestaurantsWhereInput | RegisteredRestaurantsWhereInput[]
    name?: StringFilter<"RegisteredRestaurants"> | string
    owner?: StringFilter<"RegisteredRestaurants"> | string
    phone?: StringFilter<"RegisteredRestaurants"> | string
    restaurantType?: StringFilter<"RegisteredRestaurants"> | string
    additionalInfo?: StringNullableFilter<"RegisteredRestaurants"> | string | null
  }, "restaurantId" | "email">

  export type RegisteredRestaurantsOrderByWithAggregationInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    owner?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    restaurantType?: SortOrder
    additionalInfo?: SortOrderInput | SortOrder
    _count?: RegisteredRestaurantsCountOrderByAggregateInput
    _max?: RegisteredRestaurantsMaxOrderByAggregateInput
    _min?: RegisteredRestaurantsMinOrderByAggregateInput
  }

  export type RegisteredRestaurantsScalarWhereWithAggregatesInput = {
    AND?: RegisteredRestaurantsScalarWhereWithAggregatesInput | RegisteredRestaurantsScalarWhereWithAggregatesInput[]
    OR?: RegisteredRestaurantsScalarWhereWithAggregatesInput[]
    NOT?: RegisteredRestaurantsScalarWhereWithAggregatesInput | RegisteredRestaurantsScalarWhereWithAggregatesInput[]
    restaurantId?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    name?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    owner?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    email?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    phone?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    restaurantType?: StringWithAggregatesFilter<"RegisteredRestaurants"> | string
    additionalInfo?: StringNullableWithAggregatesFilter<"RegisteredRestaurants"> | string | null
  }

  export type RestaurantsWhereInput = {
    AND?: RestaurantsWhereInput | RestaurantsWhereInput[]
    OR?: RestaurantsWhereInput[]
    NOT?: RestaurantsWhereInput | RestaurantsWhereInput[]
    restaurantId?: StringFilter<"Restaurants"> | string
    name?: StringFilter<"Restaurants"> | string
    email?: StringFilter<"Restaurants"> | string
  }

  export type RestaurantsOrderByWithRelationInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    email?: SortOrder
  }

  export type RestaurantsWhereUniqueInput = Prisma.AtLeast<{
    restaurantId?: string
    email?: string
    AND?: RestaurantsWhereInput | RestaurantsWhereInput[]
    OR?: RestaurantsWhereInput[]
    NOT?: RestaurantsWhereInput | RestaurantsWhereInput[]
    name?: StringFilter<"Restaurants"> | string
  }, "restaurantId" | "email">

  export type RestaurantsOrderByWithAggregationInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    email?: SortOrder
    _count?: RestaurantsCountOrderByAggregateInput
    _max?: RestaurantsMaxOrderByAggregateInput
    _min?: RestaurantsMinOrderByAggregateInput
  }

  export type RestaurantsScalarWhereWithAggregatesInput = {
    AND?: RestaurantsScalarWhereWithAggregatesInput | RestaurantsScalarWhereWithAggregatesInput[]
    OR?: RestaurantsScalarWhereWithAggregatesInput[]
    NOT?: RestaurantsScalarWhereWithAggregatesInput | RestaurantsScalarWhereWithAggregatesInput[]
    restaurantId?: StringWithAggregatesFilter<"Restaurants"> | string
    name?: StringWithAggregatesFilter<"Restaurants"> | string
    email?: StringWithAggregatesFilter<"Restaurants"> | string
  }

  export type UsersWhereInput = {
    AND?: UsersWhereInput | UsersWhereInput[]
    OR?: UsersWhereInput[]
    NOT?: UsersWhereInput | UsersWhereInput[]
    userId?: StringFilter<"Users"> | string
    firstName?: StringFilter<"Users"> | string
    lastName?: StringFilter<"Users"> | string
    email?: StringFilter<"Users"> | string
    favouriteRestaurant?: StringFilter<"Users"> | string
    EarlyAccessUsers?: XOR<EarlyAccessUsersNullableScalarRelationFilter, EarlyAccessUsersWhereInput> | null
  }

  export type UsersOrderByWithRelationInput = {
    userId?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    favouriteRestaurant?: SortOrder
    EarlyAccessUsers?: EarlyAccessUsersOrderByWithRelationInput
  }

  export type UsersWhereUniqueInput = Prisma.AtLeast<{
    userId?: string
    email?: string
    AND?: UsersWhereInput | UsersWhereInput[]
    OR?: UsersWhereInput[]
    NOT?: UsersWhereInput | UsersWhereInput[]
    firstName?: StringFilter<"Users"> | string
    lastName?: StringFilter<"Users"> | string
    favouriteRestaurant?: StringFilter<"Users"> | string
    EarlyAccessUsers?: XOR<EarlyAccessUsersNullableScalarRelationFilter, EarlyAccessUsersWhereInput> | null
  }, "userId" | "email">

  export type UsersOrderByWithAggregationInput = {
    userId?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    favouriteRestaurant?: SortOrder
    _count?: UsersCountOrderByAggregateInput
    _max?: UsersMaxOrderByAggregateInput
    _min?: UsersMinOrderByAggregateInput
  }

  export type UsersScalarWhereWithAggregatesInput = {
    AND?: UsersScalarWhereWithAggregatesInput | UsersScalarWhereWithAggregatesInput[]
    OR?: UsersScalarWhereWithAggregatesInput[]
    NOT?: UsersScalarWhereWithAggregatesInput | UsersScalarWhereWithAggregatesInput[]
    userId?: StringWithAggregatesFilter<"Users"> | string
    firstName?: StringWithAggregatesFilter<"Users"> | string
    lastName?: StringWithAggregatesFilter<"Users"> | string
    email?: StringWithAggregatesFilter<"Users"> | string
    favouriteRestaurant?: StringWithAggregatesFilter<"Users"> | string
  }

  export type DevNotificationCreateInput = {
    id: string
    endpoint: string
    merchantId: string
    merchantName: string
    amount: Decimal | DecimalJsLike | number | string
    mobileNumber: string
    clientReference: string
    paymentType: string
    timestamp?: Date | string
  }

  export type DevNotificationUncheckedCreateInput = {
    id: string
    endpoint: string
    merchantId: string
    merchantName: string
    amount: Decimal | DecimalJsLike | number | string
    mobileNumber: string
    clientReference: string
    paymentType: string
    timestamp?: Date | string
  }

  export type DevNotificationUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    endpoint?: StringFieldUpdateOperationsInput | string
    merchantId?: StringFieldUpdateOperationsInput | string
    merchantName?: StringFieldUpdateOperationsInput | string
    amount?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    clientReference?: StringFieldUpdateOperationsInput | string
    paymentType?: StringFieldUpdateOperationsInput | string
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DevNotificationUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    endpoint?: StringFieldUpdateOperationsInput | string
    merchantId?: StringFieldUpdateOperationsInput | string
    merchantName?: StringFieldUpdateOperationsInput | string
    amount?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    clientReference?: StringFieldUpdateOperationsInput | string
    paymentType?: StringFieldUpdateOperationsInput | string
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DevNotificationCreateManyInput = {
    id: string
    endpoint: string
    merchantId: string
    merchantName: string
    amount: Decimal | DecimalJsLike | number | string
    mobileNumber: string
    clientReference: string
    paymentType: string
    timestamp?: Date | string
  }

  export type DevNotificationUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    endpoint?: StringFieldUpdateOperationsInput | string
    merchantId?: StringFieldUpdateOperationsInput | string
    merchantName?: StringFieldUpdateOperationsInput | string
    amount?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    clientReference?: StringFieldUpdateOperationsInput | string
    paymentType?: StringFieldUpdateOperationsInput | string
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DevNotificationUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    endpoint?: StringFieldUpdateOperationsInput | string
    merchantId?: StringFieldUpdateOperationsInput | string
    merchantName?: StringFieldUpdateOperationsInput | string
    amount?: DecimalFieldUpdateOperationsInput | Decimal | DecimalJsLike | number | string
    mobileNumber?: StringFieldUpdateOperationsInput | string
    clientReference?: StringFieldUpdateOperationsInput | string
    paymentType?: StringFieldUpdateOperationsInput | string
    timestamp?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type EarlyAccessUsersCreateInput = {
    phoneNumber: string
    socialMediaLinks?: EarlyAccessUsersCreatesocialMediaLinksInput | string[]
    approved?: boolean
    Users: UsersCreateNestedOneWithoutEarlyAccessUsersInput
  }

  export type EarlyAccessUsersUncheckedCreateInput = {
    userId: string
    phoneNumber: string
    socialMediaLinks?: EarlyAccessUsersCreatesocialMediaLinksInput | string[]
    approved?: boolean
  }

  export type EarlyAccessUsersUpdateInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
    Users?: UsersUpdateOneRequiredWithoutEarlyAccessUsersNestedInput
  }

  export type EarlyAccessUsersUncheckedUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
  }

  export type EarlyAccessUsersCreateManyInput = {
    userId: string
    phoneNumber: string
    socialMediaLinks?: EarlyAccessUsersCreatesocialMediaLinksInput | string[]
    approved?: boolean
  }

  export type EarlyAccessUsersUpdateManyMutationInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
  }

  export type EarlyAccessUsersUncheckedUpdateManyInput = {
    userId?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
  }

  export type RegisteredRestaurantsCreateInput = {
    restaurantId: string
    name: string
    owner: string
    email: string
    phone: string
    restaurantType: string
    additionalInfo?: string | null
  }

  export type RegisteredRestaurantsUncheckedCreateInput = {
    restaurantId: string
    name: string
    owner: string
    email: string
    phone: string
    restaurantType: string
    additionalInfo?: string | null
  }

  export type RegisteredRestaurantsUpdateInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    owner?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    restaurantType?: StringFieldUpdateOperationsInput | string
    additionalInfo?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RegisteredRestaurantsUncheckedUpdateInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    owner?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    restaurantType?: StringFieldUpdateOperationsInput | string
    additionalInfo?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RegisteredRestaurantsCreateManyInput = {
    restaurantId: string
    name: string
    owner: string
    email: string
    phone: string
    restaurantType: string
    additionalInfo?: string | null
  }

  export type RegisteredRestaurantsUpdateManyMutationInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    owner?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    restaurantType?: StringFieldUpdateOperationsInput | string
    additionalInfo?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RegisteredRestaurantsUncheckedUpdateManyInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    owner?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    phone?: StringFieldUpdateOperationsInput | string
    restaurantType?: StringFieldUpdateOperationsInput | string
    additionalInfo?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type RestaurantsCreateInput = {
    restaurantId: string
    name: string
    email: string
  }

  export type RestaurantsUncheckedCreateInput = {
    restaurantId: string
    name: string
    email: string
  }

  export type RestaurantsUpdateInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
  }

  export type RestaurantsUncheckedUpdateInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
  }

  export type RestaurantsCreateManyInput = {
    restaurantId: string
    name: string
    email: string
  }

  export type RestaurantsUpdateManyMutationInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
  }

  export type RestaurantsUncheckedUpdateManyInput = {
    restaurantId?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
  }

  export type UsersCreateInput = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
    EarlyAccessUsers?: EarlyAccessUsersCreateNestedOneWithoutUsersInput
  }

  export type UsersUncheckedCreateInput = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
    EarlyAccessUsers?: EarlyAccessUsersUncheckedCreateNestedOneWithoutUsersInput
  }

  export type UsersUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
    EarlyAccessUsers?: EarlyAccessUsersUpdateOneWithoutUsersNestedInput
  }

  export type UsersUncheckedUpdateInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
    EarlyAccessUsers?: EarlyAccessUsersUncheckedUpdateOneWithoutUsersNestedInput
  }

  export type UsersCreateManyInput = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
  }

  export type UsersUpdateManyMutationInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
  }

  export type UsersUncheckedUpdateManyInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type DecimalFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type DevNotificationCountOrderByAggregateInput = {
    id?: SortOrder
    endpoint?: SortOrder
    merchantId?: SortOrder
    merchantName?: SortOrder
    amount?: SortOrder
    mobileNumber?: SortOrder
    clientReference?: SortOrder
    paymentType?: SortOrder
    timestamp?: SortOrder
  }

  export type DevNotificationAvgOrderByAggregateInput = {
    amount?: SortOrder
  }

  export type DevNotificationMaxOrderByAggregateInput = {
    id?: SortOrder
    endpoint?: SortOrder
    merchantId?: SortOrder
    merchantName?: SortOrder
    amount?: SortOrder
    mobileNumber?: SortOrder
    clientReference?: SortOrder
    paymentType?: SortOrder
    timestamp?: SortOrder
  }

  export type DevNotificationMinOrderByAggregateInput = {
    id?: SortOrder
    endpoint?: SortOrder
    merchantId?: SortOrder
    merchantName?: SortOrder
    amount?: SortOrder
    mobileNumber?: SortOrder
    clientReference?: SortOrder
    paymentType?: SortOrder
    timestamp?: SortOrder
  }

  export type DevNotificationSumOrderByAggregateInput = {
    amount?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type DecimalWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedDecimalFilter<$PrismaModel>
    _sum?: NestedDecimalFilter<$PrismaModel>
    _min?: NestedDecimalFilter<$PrismaModel>
    _max?: NestedDecimalFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type StringNullableListFilter<$PrismaModel = never> = {
    equals?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    has?: string | StringFieldRefInput<$PrismaModel> | null
    hasEvery?: string[] | ListStringFieldRefInput<$PrismaModel>
    hasSome?: string[] | ListStringFieldRefInput<$PrismaModel>
    isEmpty?: boolean
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type UsersScalarRelationFilter = {
    is?: UsersWhereInput
    isNot?: UsersWhereInput
  }

  export type EarlyAccessUsersCountOrderByAggregateInput = {
    userId?: SortOrder
    phoneNumber?: SortOrder
    socialMediaLinks?: SortOrder
    approved?: SortOrder
  }

  export type EarlyAccessUsersMaxOrderByAggregateInput = {
    userId?: SortOrder
    phoneNumber?: SortOrder
    approved?: SortOrder
  }

  export type EarlyAccessUsersMinOrderByAggregateInput = {
    userId?: SortOrder
    phoneNumber?: SortOrder
    approved?: SortOrder
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type RegisteredRestaurantsCountOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    owner?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    restaurantType?: SortOrder
    additionalInfo?: SortOrder
  }

  export type RegisteredRestaurantsMaxOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    owner?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    restaurantType?: SortOrder
    additionalInfo?: SortOrder
  }

  export type RegisteredRestaurantsMinOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    owner?: SortOrder
    email?: SortOrder
    phone?: SortOrder
    restaurantType?: SortOrder
    additionalInfo?: SortOrder
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type RestaurantsCountOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    email?: SortOrder
  }

  export type RestaurantsMaxOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    email?: SortOrder
  }

  export type RestaurantsMinOrderByAggregateInput = {
    restaurantId?: SortOrder
    name?: SortOrder
    email?: SortOrder
  }

  export type EarlyAccessUsersNullableScalarRelationFilter = {
    is?: EarlyAccessUsersWhereInput | null
    isNot?: EarlyAccessUsersWhereInput | null
  }

  export type UsersCountOrderByAggregateInput = {
    userId?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    favouriteRestaurant?: SortOrder
  }

  export type UsersMaxOrderByAggregateInput = {
    userId?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    favouriteRestaurant?: SortOrder
  }

  export type UsersMinOrderByAggregateInput = {
    userId?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    email?: SortOrder
    favouriteRestaurant?: SortOrder
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type DecimalFieldUpdateOperationsInput = {
    set?: Decimal | DecimalJsLike | number | string
    increment?: Decimal | DecimalJsLike | number | string
    decrement?: Decimal | DecimalJsLike | number | string
    multiply?: Decimal | DecimalJsLike | number | string
    divide?: Decimal | DecimalJsLike | number | string
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type EarlyAccessUsersCreatesocialMediaLinksInput = {
    set: string[]
  }

  export type UsersCreateNestedOneWithoutEarlyAccessUsersInput = {
    create?: XOR<UsersCreateWithoutEarlyAccessUsersInput, UsersUncheckedCreateWithoutEarlyAccessUsersInput>
    connectOrCreate?: UsersCreateOrConnectWithoutEarlyAccessUsersInput
    connect?: UsersWhereUniqueInput
  }

  export type EarlyAccessUsersUpdatesocialMediaLinksInput = {
    set?: string[]
    push?: string | string[]
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type UsersUpdateOneRequiredWithoutEarlyAccessUsersNestedInput = {
    create?: XOR<UsersCreateWithoutEarlyAccessUsersInput, UsersUncheckedCreateWithoutEarlyAccessUsersInput>
    connectOrCreate?: UsersCreateOrConnectWithoutEarlyAccessUsersInput
    upsert?: UsersUpsertWithoutEarlyAccessUsersInput
    connect?: UsersWhereUniqueInput
    update?: XOR<XOR<UsersUpdateToOneWithWhereWithoutEarlyAccessUsersInput, UsersUpdateWithoutEarlyAccessUsersInput>, UsersUncheckedUpdateWithoutEarlyAccessUsersInput>
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type EarlyAccessUsersCreateNestedOneWithoutUsersInput = {
    create?: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
    connectOrCreate?: EarlyAccessUsersCreateOrConnectWithoutUsersInput
    connect?: EarlyAccessUsersWhereUniqueInput
  }

  export type EarlyAccessUsersUncheckedCreateNestedOneWithoutUsersInput = {
    create?: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
    connectOrCreate?: EarlyAccessUsersCreateOrConnectWithoutUsersInput
    connect?: EarlyAccessUsersWhereUniqueInput
  }

  export type EarlyAccessUsersUpdateOneWithoutUsersNestedInput = {
    create?: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
    connectOrCreate?: EarlyAccessUsersCreateOrConnectWithoutUsersInput
    upsert?: EarlyAccessUsersUpsertWithoutUsersInput
    disconnect?: EarlyAccessUsersWhereInput | boolean
    delete?: EarlyAccessUsersWhereInput | boolean
    connect?: EarlyAccessUsersWhereUniqueInput
    update?: XOR<XOR<EarlyAccessUsersUpdateToOneWithWhereWithoutUsersInput, EarlyAccessUsersUpdateWithoutUsersInput>, EarlyAccessUsersUncheckedUpdateWithoutUsersInput>
  }

  export type EarlyAccessUsersUncheckedUpdateOneWithoutUsersNestedInput = {
    create?: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
    connectOrCreate?: EarlyAccessUsersCreateOrConnectWithoutUsersInput
    upsert?: EarlyAccessUsersUpsertWithoutUsersInput
    disconnect?: EarlyAccessUsersWhereInput | boolean
    delete?: EarlyAccessUsersWhereInput | boolean
    connect?: EarlyAccessUsersWhereUniqueInput
    update?: XOR<XOR<EarlyAccessUsersUpdateToOneWithWhereWithoutUsersInput, EarlyAccessUsersUpdateWithoutUsersInput>, EarlyAccessUsersUncheckedUpdateWithoutUsersInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedDecimalFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedDecimalWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    in?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    notIn?: Decimal[] | DecimalJsLike[] | number[] | string[] | ListDecimalFieldRefInput<$PrismaModel>
    lt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    lte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gt?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    gte?: Decimal | DecimalJsLike | number | string | DecimalFieldRefInput<$PrismaModel>
    not?: NestedDecimalWithAggregatesFilter<$PrismaModel> | Decimal | DecimalJsLike | number | string
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedDecimalFilter<$PrismaModel>
    _sum?: NestedDecimalFilter<$PrismaModel>
    _min?: NestedDecimalFilter<$PrismaModel>
    _max?: NestedDecimalFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type UsersCreateWithoutEarlyAccessUsersInput = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
  }

  export type UsersUncheckedCreateWithoutEarlyAccessUsersInput = {
    userId: string
    firstName: string
    lastName: string
    email: string
    favouriteRestaurant: string
  }

  export type UsersCreateOrConnectWithoutEarlyAccessUsersInput = {
    where: UsersWhereUniqueInput
    create: XOR<UsersCreateWithoutEarlyAccessUsersInput, UsersUncheckedCreateWithoutEarlyAccessUsersInput>
  }

  export type UsersUpsertWithoutEarlyAccessUsersInput = {
    update: XOR<UsersUpdateWithoutEarlyAccessUsersInput, UsersUncheckedUpdateWithoutEarlyAccessUsersInput>
    create: XOR<UsersCreateWithoutEarlyAccessUsersInput, UsersUncheckedCreateWithoutEarlyAccessUsersInput>
    where?: UsersWhereInput
  }

  export type UsersUpdateToOneWithWhereWithoutEarlyAccessUsersInput = {
    where?: UsersWhereInput
    data: XOR<UsersUpdateWithoutEarlyAccessUsersInput, UsersUncheckedUpdateWithoutEarlyAccessUsersInput>
  }

  export type UsersUpdateWithoutEarlyAccessUsersInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
  }

  export type UsersUncheckedUpdateWithoutEarlyAccessUsersInput = {
    userId?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    favouriteRestaurant?: StringFieldUpdateOperationsInput | string
  }

  export type EarlyAccessUsersCreateWithoutUsersInput = {
    phoneNumber: string
    socialMediaLinks?: EarlyAccessUsersCreatesocialMediaLinksInput | string[]
    approved?: boolean
  }

  export type EarlyAccessUsersUncheckedCreateWithoutUsersInput = {
    phoneNumber: string
    socialMediaLinks?: EarlyAccessUsersCreatesocialMediaLinksInput | string[]
    approved?: boolean
  }

  export type EarlyAccessUsersCreateOrConnectWithoutUsersInput = {
    where: EarlyAccessUsersWhereUniqueInput
    create: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
  }

  export type EarlyAccessUsersUpsertWithoutUsersInput = {
    update: XOR<EarlyAccessUsersUpdateWithoutUsersInput, EarlyAccessUsersUncheckedUpdateWithoutUsersInput>
    create: XOR<EarlyAccessUsersCreateWithoutUsersInput, EarlyAccessUsersUncheckedCreateWithoutUsersInput>
    where?: EarlyAccessUsersWhereInput
  }

  export type EarlyAccessUsersUpdateToOneWithWhereWithoutUsersInput = {
    where?: EarlyAccessUsersWhereInput
    data: XOR<EarlyAccessUsersUpdateWithoutUsersInput, EarlyAccessUsersUncheckedUpdateWithoutUsersInput>
  }

  export type EarlyAccessUsersUpdateWithoutUsersInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
  }

  export type EarlyAccessUsersUncheckedUpdateWithoutUsersInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    socialMediaLinks?: EarlyAccessUsersUpdatesocialMediaLinksInput | string[]
    approved?: BoolFieldUpdateOperationsInput | boolean
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}