'use server';

import { prisma } from '@/lib/prisma';

type FilterOptions = {
  paymentType?: string;
  merchantId?: string;
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  limit?: number;
  offset?: number;
  orderBy?: 'asc' | 'desc';
};

export async function filterDevNotifications({
  paymentType,
  merchantId,
  startDate,
  endDate,
  searchTerm,
  limit = 10,
  offset = 0,
  orderBy = 'desc',
}: FilterOptions) {
  try {
    // Validate inputs to prevent errors
    const validLimit = Math.max(1, Math.min(100, limit));
    const validOffset = Math.max(0, offset);
    const validOrderBy = ['asc', 'desc'].includes(orderBy) ? orderBy : 'desc';

    // Build the where clause based on filter options
    const where: any = {};

    if (paymentType) {
      where.paymentType = paymentType;
    }

    if (merchantId) {
      where.merchantId = merchantId;
    }

    if (startDate || endDate) {
      where.timestamp = {};

      if (startDate) {
        where.timestamp.gte = startDate;
      }

      if (endDate) {
        where.timestamp.lte = endDate;
      }
    }

    if (searchTerm) {
      where.OR = [
        { merchantName: { contains: searchTerm, mode: 'insensitive' } },
        { clientReference: { contains: searchTerm, mode: 'insensitive' } },
        { mobileNumber: { contains: searchTerm, mode: 'insensitive' } },
      ];
    }

    // Execute the query with filters
    const notifications = await prisma.devNotification.findMany({
      where,
      take: validLimit,
      skip: validOffset,
      orderBy: {
        timestamp: validOrderBy,
      },
    });

    // Convert Decimal objects to strings to avoid serialization issues
    const serializedNotifications = notifications.map(notification => ({
      ...notification,
      amount: notification.amount.toString(),
      timestamp: notification.timestamp,
    }));

    // Get total count with the same filters
    const total = await prisma.devNotification.count({ where });

    return {
      notifications: serializedNotifications,
      total,
      limit: validLimit,
      offset: validOffset,
    };
  } catch (error) {
    console.error('Error filtering dev notifications:', error);
    // Return empty data instead of throwing to prevent UI errors
    return {
      notifications: [],
      total: 0,
      limit,
      offset,
    };
  }
}

export async function getMerchantList() {
  try {
    const merchants = await prisma.devNotification.findMany({
      select: {
        merchantId: true,
        merchantName: true,
      },
      distinct: ['merchantId'],
      orderBy: {
        merchantName: 'asc',
      },
    });

    return merchants;
  } catch (error) {
    console.error('Error fetching merchant list:', error);
    // Return empty array instead of throwing to prevent UI errors
    return [];
  }
}
