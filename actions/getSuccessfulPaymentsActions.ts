'use server';

import { getTableData } from './getDatabase';
import { Order, isProductPurchase, isLocalBitesPayment, formatPaymentType } from '@/lib/paymentUtils';

export interface PaymentAnalytics {
  totalSuccessfulPayments: number;
  totalAmount: number;
  regularPayments: {
    count: number;
    amount: number;
  };
  productPurchasePayments: {
    count: number;
    amount: number;
  };
  serviceFees: {
    totalFees: number;
    applicablePayments: number;
    applicableAmount: number;
  };
  localBitesPayments: {
    count: number;
    amount: number;
  };
  paymentMethodBreakdown: {
    [key: string]: {
      count: number;
      amount: number;
    };
  };
}

/**
 * Fetches successful payments data from orders table
 * @returns {Promise<Order[]>} Array of successful payment orders
 */
export async function getSuccessfulPayments(): Promise<Order[]> {
  try {
    console.log('Fetching orders data...');
    const ordersData = await getTableData('orders');

    console.log('Orders data received:', {
      isArray: Array.isArray(ordersData),
      length: Array.isArray(ordersData) ? ordersData.length : 'N/A',
      sample: Array.isArray(ordersData) && ordersData.length > 0 ? ordersData[0] : null
    });

    if (!Array.isArray(ordersData)) {
      console.error('Orders data is not an array:', ordersData);
      return [];
    }

    // Filter for successful payments (orders with successful payment status)
    const successfulPayments = ordersData.filter((order: Order) => {
      // Based on the actual data structure, we'll consider orders as successful payments if:
      // 1. They have a total_payable amount > 0
      // 2. They are not cancelled (active_status is not 'cancelled')
      // 3. They have progressed beyond 'pending' status

      const totalPayable = Number(order.total_payable) || 0;
      const activeStatus = order.active_status?.toLowerCase() || '';

      // Consider as successful payment if:
      // - Has payable amount
      // - Not cancelled
      // - Has progressed beyond pending (confirmed, preparing, delivered, etc.)
      const hasSuccessfulPayment =
        totalPayable > 0 &&
        activeStatus !== 'cancelled' &&
        activeStatus !== 'pending' &&
        activeStatus !== '';

      return hasSuccessfulPayment;
    });

    console.log(`Filtered ${successfulPayments.length} successful payments from ${ordersData.length} total orders`);
    return successfulPayments;
  } catch (error) {
    console.error('Error fetching successful payments:', error);
    return [];
  }
}



/**
 * Calculates comprehensive payment analytics with updated service fee logic
 * @returns {Promise<PaymentAnalytics>} Payment analytics data
 */
export async function getPaymentAnalytics(): Promise<PaymentAnalytics> {
  try {
    const successfulPayments = await getSuccessfulPayments();
    
    let totalAmount = 0;
    let regularPaymentsCount = 0;
    let regularPaymentsAmount = 0;
    let productPurchaseCount = 0;
    let productPurchaseAmount = 0;
    let localBitesCount = 0;
    let localBitesAmount = 0;
    let totalServiceFees = 0;
    let serviceFeesApplicablePayments = 0;
    let serviceFeesApplicableAmount = 0;
    
    const paymentMethodBreakdown: { [key: string]: { count: number; amount: number } } = {};

    successfulPayments.forEach((order) => {
      const amount = Number(order.total_payable) || 0;
      totalAmount += amount;
      
      // Payment method breakdown
      const paymentMethod = formatPaymentType(order.payment_method || 'Unknown');
      if (!paymentMethodBreakdown[paymentMethod]) {
        paymentMethodBreakdown[paymentMethod] = { count: 0, amount: 0 };
      }
      paymentMethodBreakdown[paymentMethod].count += 1;
      paymentMethodBreakdown[paymentMethod].amount += amount;
      
      // Check if it's a product purchase
      const isProduct = isProductPurchase(order);

      if (isProduct) {
        productPurchaseCount += 1;
        productPurchaseAmount += amount;
      } else {
        regularPaymentsCount += 1;
        regularPaymentsAmount += amount;

        // Calculate service fees (8% of payment amount) for ALL non-product purchases
        totalServiceFees += amount * 0.08;
        serviceFeesApplicablePayments += 1;
        serviceFeesApplicableAmount += amount;
      }

      // Check if it's a LocalBites payment (NEXT GEN)
      if (isLocalBitesPayment(order)) {
        localBitesCount += 1;
        localBitesAmount += amount;
      }
    });

    return {
      totalSuccessfulPayments: successfulPayments.length,
      totalAmount,
      regularPayments: {
        count: regularPaymentsCount,
        amount: regularPaymentsAmount,
      },
      productPurchasePayments: {
        count: productPurchaseCount,
        amount: productPurchaseAmount,
      },
      serviceFees: {
        totalFees: totalServiceFees,
        applicablePayments: serviceFeesApplicablePayments,
        applicableAmount: serviceFeesApplicableAmount,
      },
      localBitesPayments: {
        count: localBitesCount,
        amount: localBitesAmount,
      },
      paymentMethodBreakdown,
    };
  } catch (error) {
    console.error('Error calculating payment analytics:', error);
    return {
      totalSuccessfulPayments: 0,
      totalAmount: 0,
      regularPayments: { count: 0, amount: 0 },
      productPurchasePayments: { count: 0, amount: 0 },
      serviceFees: { totalFees: 0, applicablePayments: 0, applicableAmount: 0 },
      localBitesPayments: { count: 0, amount: 0 },
      paymentMethodBreakdown: {},
    };
  }
}
