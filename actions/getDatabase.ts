"use server"

import axios from 'axios';

/**
 * Fetches the entire database export from the API
 * @returns {Promise<any>} The exported database data
 */
export const getDatabase = async () => {
  try {
    // Get the API base URL from environment variables
    const apiBaseUrl = process.env.API_URL;
    const dbEndpoint = process.env.DATABASE_ENDPOINT;
    
    if (!apiBaseUrl) {
      throw new Error("API URL is not defined. Please check your .env file.");
    }

    // Construct the full API URL for the database export
    const apiUrl = `${apiBaseUrl}/${dbEndpoint}`;
    
    // Make the request to the API
    const response = await axios.get(apiUrl);

    // Check if we got a valid response
    if (response.data) {
      return response.data;
    } else {
      console.error('No data received from the API.');
      return null;
    }
  } catch (error) {
    // Handle errors
    console.error('Error fetching database:');
    
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // The server responded with a status code outside of 2xx
        console.error('Status:', error.response.status);
        console.error('Headers:', error.response.headers);
        console.error('Data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from the server.');
      } else {
        // Something happened in setting up the request
        console.error('Error message:', error.message);
      }
    } else {
      // Handle non-Axios errors
      console.error('Unexpected error:', error);
    }
    
    throw error; // Re-throw the error so callers can handle it
  }
};

/**
 * Fetches a specific table from the database export
 * @param {string} tableName - The name of the table to fetch
 * @returns {Promise<any[]>} The table data
 */
export const getTableData = async (tableName: string) => {
  try {
    const databaseData = await getDatabase();
    
    if (databaseData && databaseData.data[tableName]) {
      return databaseData.data[tableName];
    } else {
      return [];
    }
  } catch (error) {
    console.error(`Error fetching data for table "${tableName}":`, error);
    throw error;
  }
};
