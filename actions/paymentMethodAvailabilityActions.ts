"use server";

export async function upsertPaymentMethodAvailability(
  paymentMethod: string,
  isAvailable: boolean,
  id: string = ""
) {
  try {
    // if (id.length > 0) {
    //   const response = await fetch(
    //     `${process.env.BASE_URL}/api/payment-method-availability`,
    //     {
    //       method: "POST",
    //       headers: {
    //         "Content-Type": "application/json",
    //         Authorization: `Bearer ${process.env.PAYMENT_METHOD_AVAILABILITY_BEARER_TOKEN}`,
    //       },
    //       body: JSON.stringify({ paymentMethod, isAvailable, id }),
    //     }
    //   );
    //   console.log("response: ", response);
    //   if (!response.ok) {
    //     throw new Error("Failed to toggle payment method availability");
    //   }
    // } else {
      const response = await fetch(
        `${process.env.BASE_URL}/api/payment-method-availability`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.PAYMENT_METHOD_AVAILABILITY_BEARER_TOKEN}`,
          },
          body: JSON.stringify({ paymentMethod, isAvailable, id }),
        }
      );
      console.log("reponse: ", response);
      if (!response.ok) {
        throw new Error("Failed to toggle payment method availability");
      }
    // }
  } catch (error) {
    console.error("Error toggling payment method availability:", error);
    return error;
  }
}

export const getPaymentMethodAvailability = async (paymentMethod: string) => {
  try {
    console.log("thing: ", process.env.PAYMENT_METHOD_AVAILABILITY_BEARER_TOKEN)
    const response = await fetch(
      `${process.env.BASE_URL}/api/payment-method-availability/?paymentMethod=${paymentMethod}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PAYMENT_METHOD_AVAILABILITY_BEARER_TOKEN}`,
        },
      }
    );

    console.log("response: ", response);
    if (!response.ok) {
      throw new Error("Failed to get payment method availability");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error getting payment method availability:", error);
    return error;
  }
};
